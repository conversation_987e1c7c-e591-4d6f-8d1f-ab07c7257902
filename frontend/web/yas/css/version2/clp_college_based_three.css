@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5 {
    padding: 0;
    margin: 0;
    font-weight: 600;
    line-height: normal;
}

a {
    text-decoration: none;
    transition: all 0.3s ease;
}

body,
p {
    font-family: "Roboto", serif;
    font-optical-sizing: auto;
    font-weight: 400;
    font-style: normal;
    font-variation-settings: "wdth"100;
    color: #000000;
    line-height: 1.5;
}

h1 {
    font-size: 50px;
    font-weight: 700;
}

h2 {
    font-size: 36px;
    font-weight: 600;
}

h2>span {
    font-size: 18px;
    color: #212529;
    font-weight: 400;
    line-height: 24px;
    margin-top: 10px;
    display: block;
}

.button-style {
    background: #F5A623;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.section-space {
    padding: 70px 0;
}

/* header */
.container {
    max-width: 1280px;
    width: 100%;
    margin: 0 auto;
    padding-left: 15px;
    padding-right: 15px;
    position: relative;
}

header {
    padding: 20px 15px;
}

header.container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* banner */
.hero-banner {
    height: auto;
    width: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-size: cover;
    padding: 40px 0;

}

.hero-banner .banner-overlay {
    background: linear-gradient(90deg, rgba(5, 48, 97, 0.9) 0%, rgba(7, 70, 143, 0.8) 34%, rgba(26, 127, 202, 0) 66%);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.hero-banner .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.hero-banner .banner-left-content {
    max-width: 100%;
    width: 100%;
    color: white;
}

.hero-banner .banner-left-content h1 {
    font-size: 50px;
    line-height: 56px;
    font-weight: 700;
}

.hero-banner .banner-left-content h1 span {
    background: linear-gradient(to right, #F5A623, #F0EADE);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.hero-banner .banner-left-content .banner-liner {
    font-size: 20px;
    line-height: 24px;
    font-weight: 400;
    margin-top: 20px;
    text-decoration: underline;
    text-underline-offset: 8px;
    text-decoration-thickness: 1px;
}

.hero-banner .banner-right-content {
    max-width: 368px;
    width: 100%;
    background-color: white;
    padding: 20px 24px;
    border-radius: 4px;
    margin: 0;
    z-index: 1;
}

.input-error {
    border-color: #F8382A !important;
}

/* Streams Offered */
.streams-offered {
    position: relative;
    background: #F5A62380;
    background: linear-gradient(305deg, rgba(245, 166, 35, 0.94) 0%, rgba(255, 255, 255, 1) 11%, rgba(255, 255, 255, 1) 87%, rgba(245, 166, 35, 0.94) 100%);
}

.streams-offered h2 {
    text-align: center;
}

.streams-offered-inner {
    display: flex;
    column-gap: 20px;
    margin-top: 40px;
    flex-wrap: wrap;
    height: 280px;
    overflow: hidden;
    transition: height 0.5s ease;
}

.streams-offered-inner .detail-block {
    max-width: 402px;
    height: 267px;
    width: 100%;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box, linear-gradient(to bottom, #0A62C7, #F5A623) border-box;
    margin-bottom: 15px;
}

.streams-offered-inner .detail-block .img-overlay {
    background: linear-gradient(0deg, rgba(245, 166, 35, 0.14) 0%, rgba(255, 255, 255, 0.14) 84%);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.streams-offered-inner .detail-block .img-block {
    text-align: center;
    width: 100%;
    height: 190px;
    position: relative;
}

.streams-offered-inner .detail-block .img-block img {
    object-fit: cover;
}

.streams-offered-inner .detail-block .text-block {
    font-size: 24px;
    line-height: 24px;
    font-weight: 700;
    text-transform: uppercase;
    color: #212529;
    width: 100%;
    height: 72px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.streams-offered .explore-now-btn {
    text-align: center;
    margin-top: 30px;
}

.streams-offered .explore-now-btn>a {
    display: inline-block;
}

.streams-offered-inner.expanded {
    height: auto;
}

.text-center {
    display: flex;
    justify-content: center;
    gap:10px;
    margin-top: 20px;
}

.view-toggle {
    cursor: pointer;
    user-select: none;
    display: flex;
    position: relative;
    align-items: center;
    justify-content: center;
}

.arrow {
    border: solid #fff;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 3px;
    margin-left: 15px;
}

.down {
    transform: rotate(45deg) translateY(-2px);
    -webkit-transform: rotate(45deg) translateY(-2px);
}

.up {
    transform: rotate(-135deg);
    -webkit-transform: rotate(-135deg);
}

/* College USPs */
.college-usps-section {
    padding: 70px 0;
    /* background-image: url(/yas/images/clp/college-based-three/usps-bg-img.jpg); background-size: cover; background-repeat: no-repeat;*/
    position: relative;
    background-color: #F5A6231A;
}

/* .college-usps-bgoverlay {background-color: rgba(10, 98, 199, 0.9); position: absolute; left: 0; top: 0; max-width: 100%; width: 100%; height: 100%;} */
.college-usps-section h2,
.college-usps-section h2>span {
    color: #212529;
    text-align: center;
}

.college-usps-inner {
    display: flex;
    justify-content: space-around;
    column-gap: 19px;
    margin-top: 40px;
}

.college-usps-inner .usps-card {
    width: 25%;
    height: 210px;
    border-radius: 12px;
    overflow: hidden;
    background-color: #fff;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid #D8D8D8;
}

.college-usps-inner .usps-card .usps-card-body {
    font-weight: 800;
    font-size: 20px;
    line-height: 30px;
    color: #0A62C7;
    padding: 20px 20px 0;
    transition: all 0.3s ease;
}

.usps-icon {
    position: relative;
    width: 80px;
    height: 80px;
    margin: 20px 20px 0 20px;
}

.usps-icon img {
    width: 80%;
}

.whiteups {
    background-color: #0A62C7;
    width: 80px;
    height: 80px;
    padding: 20px;
    border-radius: 50%;
}

.college-usps-inner .usps-card .usps-card-body>span {
    /* font-size: 16px;
    font-weight: 500;
    color: #212529;
    transition: all 0.3s ease; */
    font-size: 16px;
    font-weight: 500;
    display: block;
    color: #212529;
    transition: all 0.3s ease;
    line-height: 24px;
}

.college-usps-inner .usps-card:hover {
    background-color: #F5A623;
}

.college-usps-inner .usps-card:hover .usps-card-body,
.college-usps-inner .usps-card:hover .usps-card-body>span {
    color: white;
}

.college-usps-section .know-more-btn {
    text-align: center;
    margin-top: 30px;
}

.thinborder {
    font-weight: normal;
    font-size: 36px;
    display: inline;
}

.colorups {
    background-color: #FFf;
    width: 80px;
    height: 80px;
    padding: 20px;
    border-radius: 50%;
}

.usps-icon .whiteups,
.usps-icon .colorups {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.usps-icon .colorups {
    visibility: hidden;
}

.usps-card:hover .usps-icon .colorups {
    visibility: visible;
}

.usps-card:hover .usps-icon .whiteups {
    visibility: hidden;
}

.cirlcradius {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background-color: #F4F7FB;
    position: absolute;
    bottom: -80px;
    right: -60px;
    transition: 0.5s;
}

.cirlcradius::before {
    content: '';
    width: 130px;
    height: 130px;
    border-radius: 50%;
    background-color: #C6D8EC33;
    bottom: 7px;
    right: 5px;
    position: absolute;
}

.usps-card:hover .cirlcradius {
    background-color: #FFFFFF4D;
}

.usps-card:hover .cirlcradius::before {
    background-color: #ffffff45;
}

/* our partner */
.top-recruiters-section {
    background: #F5A62380;
    background: linear-gradient(229deg, rgba(245, 166, 35, 0.94) 0%, rgba(255, 255, 255, 1) 11%);
}


.top-recruiters-section h2 {
    text-align: center;
}

.top-recruiters-slider {
    margin-top: 40px;
}

.top-recruiters-slider.slick-slider .slick-track {
    display: flex;
    align-items: center;
}

.top-recruiters-slider .recruiters-logo-block {
    margin: 0 20px;
    border: 1px solid #D8D8D8;
    padding: 10px 20px;
    border-radius: 5px;
}

.top-recruiters-slider .recruiters-logo-block img {
    width: 100%;
    height: 68px;
    object-fit: scale-down;
}

.top-recruiters-section .enquiry-now-button {
    text-align: center;
    margin-top: 70px;
}

.top-recruiters-slider.slick-slider .slick-dots {
    bottom: -40px;
}

.top-recruiters-slider.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}

.top-recruiters-slider.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #D8D8D8;
    border-radius: 100%;
}

.top-recruiters-slider.slick-slider .slick-dots li.slick-active>button {
    background-color: #F5A623;
}

.top-recruiters-slider.slick-slider .slick-dots li>button::before {
    display: none;
}

.section-image .slick-slide img {
    display: block;
    width: 265px;
    height: 173px;
    position: absolute;
    border-radius: 12px;
    z-index: 1;
    inset: 0;
    margin: -30px auto 0
}

/* Our Campuses */
.our-campus-section {
    background: url(/yas/images/clp/college-based-three/campus-bg.jpg) no-repeat center;
    padding-top: 70px;
    padding-bottom: 70px;
    background-size: cover;
    position: relative;
}

.campus-overlay {
    background-color: #094FA0E5;
    opacity: 0.9;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.our-campus-section h2 {
    text-align: center;
    color: #fff;
}

.our-campus-section h2>span {
    line-height: 24px;
    font-size: 18px;
    font-weight: 400;
    display: block;
    margin-top: 10px;
}

.our-campus-slider {
    margin-top: 50px;
}

.our-campus-slider.slick-slider {
    margin-bottom: 0;
}

.our-campus-slider.slick-slider .slick-list.draggable {
    padding-top: 30px;
    padding-bottom: 70px;
}

.our-campus-slider.slick-slider .slick-dots {
    bottom: 20px;
}

.our-campus-slider.slick-slider .slick-dots li {
    width: 10px;
    height: 10px;
    list-style: none;
}

.our-campus-slider.slick-slider .slick-dots li>button {
    width: 10px;
    height: 10px;
    background-color: #D8D8D8;
    border-radius: 100%;
}

.our-campus-slider.slick-slider .slick-dots li.slick-active>button {
    background-color: #F5A623;
}

.our-campus-slider.slick-slider .slick-dots li>button::before {
    display: none;
}

.campus-card {
    width: 100%;
    position: relative;
    margin: 0 10px;
    transition: 0.5s;
    border-radius: 12px;
}

.campus-card:hover {
    background-color: #F5A623;
    border-radius: 12px;
}

.campus-card:hover img {
    filter: drop-shadow(0.35rem 0.35rem 0.4rem rgba(0, 0, 0, 0.5))
}

.campus-card .campus-img {
    border-radius: 12px;
    overflow: hidden;
    width: 100%;
    height: 215px;
    border: 1px solid #FAD493;
}

.campus-card .campus-card-text {
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    text-align: left;
    padding: 0px 15px;
    color: white;
    border-radius: 100px;
    position: absolute;
    margin: 0 auto;
    max-width: 285px;
    width: 100%;
    bottom: 10px;
    left: 0;
    right: 0;
}

.location-img {
    width: 16px;
    height: 16px;
    position: relative;
    margin-right: 5px;
    display: inline-block;
}

.campus-card .campus-card-text img {
    width: 16px;
    height: 16px;
    position: relative;
    margin: 0;
    display: inline-block;
    vertical-align: middle;
    position: absolute;
    left: 0;
    top: 2px;
    filter: none
}

.our-campus-section .explore-life-button {
    text-align: center;
    margin-top: 10px;
}

.campus-card .whiteLocation {
    visibility: hidden;
}

.campus-card:hover .whiteLocation {
    visibility: visible;
}

.campus-card:hover .colorLocation {
    visibility: hidden;
}

/* About Jaipuria Institute of Management */
.about-insti-section {
    position: relative;
    background: #F5A62380;
    background: linear-gradient(131deg, rgba(245, 166, 35, 0.94) 0%, rgba(255, 255, 255, 1) 11%);
}

/* .about-insti-section::before {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    content: "";
    background: url(/yas/images/clp/college-based-three/wave-gradient.png);
} */

.about-insti-inner {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.about-insti-inner h2 {
    width: 100%;
    margin-bottom: 30px;
    text-align: center;
}

.about-insti-inner .about-insti-left {
    width: 55%;
    padding: 0 0 30px 0;
    color: #212529;
}

.about-insti-inner .about-insti-left h2 {
    margin-bottom: 25px;
}

.about-insti-inner .about-insti-left>p {
    padding: 0;
    margin: 0 0 20px 0;
    font-size: 16px;
    line-height: 24px;
    font-weight: 400;
    color: #212529;
}

.about-insti-inner .about-insti-right {
    width: 43%
}

.about-insti-inner .about-insti-right img {
    display: block;
    border-left: 0;
    width: 100%;
}

/* footer */
footer {
    background-color: #212529;
    color: white;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    line-height: 54px;
}

/* for modal */
.overflow-hide {
    overflow: hidden;
}

.modal-overlay-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1;
    display: none;
    align-items: center;
    overflow-y: auto;
}

.modal-body {
    padding: 0;
    margin: auto;
    height: auto;
    max-width: 370px;
    width: 100%;
    background: white;
    border-radius: 0;
    box-shadow: 0 0 20px rgba(0, 0, 0, .4);
    position: relative;
    z-index: 300;
    overflow-x: auto;
    padding: 25px;
    border-radius: 5px;
}

.modal-body .modal-head {
    font-size: 20px;
    font-weight: 600;
    text-align: center;
    color: #1E1E1E;
}

.modal-body .modal-head>span {
    font-size: 16px;
    font-weight: 400;
    display: block;
}

.modal-body .form-content {
    margin-top: 20px;
}

.modal-body .form-content .form-field-block {
    position: relative;
}

.modal-body .form-content .form-field-block .form-error {
    position: absolute;
    display: block;
    width: 100%;
    bottom: 0;
    left: 0;
    font-size: 11px;
    color: #F8382A;
}

.modal-body .form-content .field-style {
    border: 1px solid #ADB5BD;
    border-radius: 8px;
    width: 100%;
    margin-bottom: 16px;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
    padding: 10px 15px;
}

.modal-body .form-content .field-style:focus {
    border-color: #F5A623;
}

.modal-body .form-content .field-style:focus-visible {
    outline: none;
}

.modal-body .form-content .field-style.select-arrow {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background: url(/yas/images/clp/college-based-three/select-arrow.png) no-repeat right 10px top 21px;
}

.modal-button {
    font-size: 18px;
    font-weight: 600;
    background-color: #F5A623;
    width: 100%;
    border-radius: 8px;
    padding: 10px 0;
    border: none;
    cursor: pointer;
    color: white;
    margin-top: 5px;
}

#modal-close {
    font-size: 28px;
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    line-height: 10px;
    font-weight: 300;
}

.modal-overlay-box.show-modal {
    display: flex;
}

.know-more-btn {
    width: 100%;
    text-align: center;
    margin-top: 20px;
}

.page-header,
.pageFooter,
.scrollToTop {
    display: none !important;
}

#genericScreenSubmit:disabled,
#modalScreenSubmit:disabled {
    background-color: rgba(202, 170, 25, 0.4);
}




.select2-container .select2-selection--single {
    height: 44px !important
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding: 8px 20px !important;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 7px !important;
    right: 8px !important;
}

.select2-container--default .select2-selection--single {
    border-radius: 8px !important;
}

.custom-select-box {
    position: relative;
}

.clpCourse {
    background-color: #fff;
    ;
    position: relative;
    appearance: none;
}

.custom-select-box::after {
    position: absolute;
    content: '';
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #888;
    right: 14px;
    top: 20px;
}

.form-field-block .select2 {
    margin-bottom: 15px;
    width: 100% !important;
}

.stick-button {
    padding: 15px 0;
}

.stick-button p {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.button-blue {
    background-color: #0D3F64;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    padding: 10px 30px;
    color: white;
    border: 0;
    cursor: pointer;
}

.whiteDownloadIcon,
.redDownloadIcon {
    width: 19px;
    height: 18px;
    background-position: 233px -354px !important;
    vertical-align: text-bottom;
    margin-right: 4px;
}

.spriteIcon {
    display: inline-block !important;
    background-image: url(../../images/master_sprite.webp);
    text-align: left;
    overflow: hidden;
}

.phoneIcon {
    width: 24px;
    height: 24px;
    background-position: 536px -246px;
    vertical-align: bottom;
    margin-right: 2px;
}

#sendCallerLeadToCld {
    display: none !important;
}

/* media start here */
@media only screen and (max-width:992px) {

    /*--sticky button--*/
    .stick-button span {
        display: none;
    }

    .stick-button {
        padding: 0;
    }

    .stick-button p {
        gap: 0;
    }

    .stick-button .button-style,
    .stick-button .button-blue {
        border-radius: 0;
        height: 45px;
        font-size: 14px;
    }

    .stick-button .button-style {
        width: 60%;
    }

    .stick-button .button-blue {
        width: calc(100% - 60%);
    }

    .hero-banner .container {
        column-gap: 40px;
    }

    .hero-banner .banner-left-content {
        max-width: 100%;
        width: calc(100% - 360px);
    }

    .hero-banner .banner-left-content h1 {
        font-size: 40px;
    }

    .hero-banner .banner-left-content .top-college>span {
        font-size: 24px;
        width: 100%;
        background-size: contain;
    }

    .hero-banner .banner-right-content {
        max-width: 100%;
        width: 333px;
    }

    .about-insti-inner {
        flex-direction: column;
    }

    .about-insti-inner .about-insti-left,
    .about-insti-inner .about-insti-right {
        width: 100%;
    }

    .about-insti-inner .about-insti-left {
        order: 2;
        border-top-left-radius: 0;
        border-bottom-right-radius: 30px;
    }

    .about-insti-inner .about-insti-right {
        order: 1;
        height: 45px;
    }

    .about-insti-inner .about-insti-right img {
        border: 1px solid #D8D8D8;
        border-top-left-radius: 30px;
        border-bottom-right-radius: 0;
        border-bottom: 0;
    }

    .college-usps-inner {
        flex-wrap: wrap;
        gap: 15px;
    }

    .college-usps-inner .usps-card {
        width: calc(50% - 9px)
    }
}

@media only screen and (max-width:767px) {
    .streams-offered-inner .detail-block {
        width: 340px;
    }

    header {
        padding: 10px 15px;
    }

    h2 {
        font-size: 30px;
    }

    .streams-offered {
        background: linear-gradient(344deg, rgba(245, 166, 35, 0.94) 0%, rgba(255, 255, 255, 1) 11%, rgba(255, 255, 255, 1) 81%, rgba(245, 166, 35, 0.94) 100%);
    }

    .top-recruiters-section {
        background: linear-gradient(205deg, rgba(245, 166, 35, 50%) 0%, rgba(255, 255, 255, 1) 33%);
    }

    .about-insti-section {
        background: linear-gradient(151deg, rgba(245, 166, 35, 0.50) 0%, rgba(255, 255, 255, 1) 21%);
    }

   

    .streams-offered-inner .detail-block {
        max-width: 100%;
    }

    .streams-offered-inner .detail-block .img-block img {
        width: 100%;
    }

    header>a>img:first-child {
        width: 148px;
        height: auto;
        display: block;
    }

    header>a>img:last-child {
        width: 133px;
        height: auto;
        display: block;
    }

    .hero-banner {
        align-items: flex-start;
        height: auto;padding: 10px 0;
    }

    .hero-banner .container {
        flex-direction: column;
    }

    .hero-banner .banner-left-content {
        width: 100%;
        padding: 40px 10px 0;
    }

    .hero-banner .banner-left-content h1 {
        text-align: center;
        font-size: 36px;
        line-height: 40px;
    }

    .hero-banner .banner-left-content .banner-liner {
        font-size: 18px;
        text-align: center;
        text-decoration: none;
    }

    .hero-banner .banner-right-content {
        width: 100%;
        margin: 40px 0;
    }

    .streams-offered-inner {
        flex-direction: column;
        gap: 20px;
    }

    .streams-offered-inner .detail-block .text-block {
        font-size: 20px;
    }

    .college-usps-section {
        padding: 40px 0;
    }

    .college-usps-inner .usps-card {
        width: 100%;
        height: 180px;
    }

    .our-campus-section {
        padding: 40px 0;
    }

    .our-campus-slider.slick-slider {
        margin-top: 20px;
    }

    .our-campus-slider .slick-list {
        padding: 0 10% 0 0;
    }

    .about-insti-inner .about-insti-left h2 {
        text-align: center;
        margin-bottom: 15px;
    }

    .about-insti-inner .about-insti-left {
        padding: 15px 0 0;
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px
    }

    .about-insti-inner .about-insti-right img {
        height: 100%;
        object-position: top;
        border-top-right-radius: 12px;
        border-top-left-radius: 12px;
    }
    .button-style {font-size: 16px;}
}