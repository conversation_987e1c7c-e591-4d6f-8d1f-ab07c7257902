<?php

namespace frontend\controllers;

use common\helpers\DataHelper;
use Yii;
use common\services\ClpService;
use yii\web\NotFoundHttpException;

class CustomLandingPageController extends Controller
{

    protected $clpService;

    public function __construct(
        $id,
        $module,
        ClpService $clpService,
        $config = []
    ) {
        $this->clpService = $clpService;
        parent::__construct($id, $module, $config);
    }

    public function actionTemplates($slug)
    {
        $clpTemplate = $this->clpService->getCLPData($slug);

        if (!$clpTemplate) {
            throw new NotFoundHttpException('Page not found.');
        }

        $template = isset(DataHelper::$clpTemplates[$clpTemplate->template_id]) ? DataHelper::$clpTemplates[$clpTemplate->template_id] : '';

        if (empty($template)) {
            throw new NotFoundHttpException('Page not found.');
        }

        $streams = $this->clpService->getStreams($clpTemplate->stream_id, null, $clpTemplate->template_id, $clpTemplate->college_id);
        $level = $this->clpService->getDegrees($clpTemplate->level_id, null, $clpTemplate->template_id, $clpTemplate->college_id, null, $clpTemplate->stream_id);
        $city = $this->clpService->getCity($clpTemplate->city_id);
        $states = $this->clpService->getState($clpTemplate->state_id);
        $courses = $this->clpService->getCourses($clpTemplate->course_id);
        $campus = $this->clpService->getCampus($clpTemplate->campus);
        $programsList = $this->clpService->getProgramsList($clpTemplate->program_id);
        $college = $this->clpService->getCollege($clpTemplate->college_id);
        $featuredContent = $this->clpService->getFeaturedContent($clpTemplate->id);
        $programsWidget = $this->clpService->getProgramsWidget($clpTemplate->program_widget, $clpTemplate->stream_id, 10);
        $streamsWidget = $this->clpService->getStreamsWidget($clpTemplate->stream_widget, $clpTemplate->college_id, 5);
        $topRecruiters = $this->clpService->getTopRecruiters($clpTemplate->college_id, $clpTemplate->recruitment_widget, 10);
        $collegeUsp = $this->clpService->getCollegeUSP($clpTemplate->college_id, $clpTemplate->college_usp_widget, 4);
        $campusColleges = $this->clpService->getCampusColleges($clpTemplate->campus_widget, 10);
        $partnerColleges = $this->clpService->getPartnerColleges($clpTemplate->partner_college_widget, 10);
        $scriptInjection = $clpTemplate->script_injection ?? '';
        $getClpCollegeImages = $this->clpService->getClpCollegeImages($clpTemplate->college_id);

        return $this->render(
            $template,
            [
                'clp' => $clpTemplate,
                'stream' => $streams ?? [],
                'level' => $level ?? [],
                'city' => $city ?? [],
                'whyGetmyuni' => [
                    'whygetmyuni1' => $featuredContent['whygetmyuni1'] ?? [],
                    'whygetmyuni2' => $featuredContent['whygetmyuni2'] ?? [],
                    'whygetmyuni3' => $featuredContent['whygetmyuni3'] ?? [],
                ],
                'streamsOfferedSubText' => $featuredContent['streamsoffered'] ?? [],
                'collegeUSPsSubText' => $featuredContent['collegeusps'] ?? [],
                'topRecruitersSubText' => $featuredContent['toprecruiters'] ?? [],
                'ourCampusesSubText' => $featuredContent['ourcampuses'] ?? [],
                'aboutSubText' => $featuredContent['about'] ?? [],
                'programsOfferedSubText' => $featuredContent['programsoffered'] ?? [],
                'ourPartnerCollegesSubText' => $featuredContent['ourpartnercolleges'] ?? [],
                'programs' => $programsWidget ?? [],
                'states' => $states ?? [],
                'courses' => $courses ?? [],
                'programsList' => $programsList ?? [],
                'college' => $college ?? [],
                'streamsWidget' => $streamsWidget ?? [],
                'topRecruiters' => $topRecruiters ?? [],
                'collegeUsp' => $collegeUsp ?? [],
                'campusColleges' => $campusColleges ?? [],
                'partnerColleges' => $partnerColleges ?? [],
                'campus' => $campus ?? [],
                'scriptInjection' => $scriptInjection ?? [],
                'isGmuLogo' => $clpTemplate->is_gmu_logo,
                'getClpCollegeImages' => $getClpCollegeImages ?? [],
            ]
        );
    }
}
