<?php

use common\models\CustomLandingPage;
use frontend\assets\AppAsset;
use frontend\helpers\Url;

$this->registerCssFile(Yii::$app->params['cssPath'] . 'clp_college_based_two.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'clp_slick.css', ['depends' => [AppAsset::class]]);
$this->registerCssFile(Yii::$app->params['cssPath'] . 'slick-theme.min.css', ['depends' => [AppAsset::class]]);

$this->params['analytics'] = $scriptInjection['analytics'] ?? '';
$this->params['conversion'] = $scriptInjection['conversion'] ?? '';
$this->params['remarketing'] = $scriptInjection['remarketing'] ?? '';
$this->title = !empty($clp->page_title) ? $clp->page_title : ($college['name'] ?? '');
$this->context->description = !empty($clp->page_title) ? $clp->page_title : ($college['name'] ?? '');
?>


<?= $this->render('partials/_top_header', [
    'clp' => $clp,
    'college' => $college,
    'isGmuLogo' => $isGmuLogo,
    'getClpCollegeImages' => $getClpCollegeImages ?? [],
]) ?>

<!-- Banner start -->
<?= $this->render(
    'partials/_college_based_one_banner',
    [
        'college' => $college,
        'clp' => $clp,
        'streams' => $stream,
        'level' => $level,
        'city' => $city,
        'states' => $states,
        'courses' => $courses,
        'programsList' => $programsList,
        'template' => CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO,
        'campus' => $campus ?? [],
        'getClpCollegeImages' => $getClpCollegeImages ?? [],
    ]
); ?>
<!-- Banner end -->

<!-- Streams Offered -->
<?php if (!empty($streamsWidget)): ?>
    <?= $this->render(
        'partials/_stream_widget',
        [
            'streamsWidget' => $streamsWidget,
            'streamsOfferedSubText' => $streamsOfferedSubText,
            'template' => CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO
        ]
    ); ?>
<?php endif; ?>

<!-- Top Recruiters -->
<?php if (!empty($topRecruiters)): ?>
    <?= $this->render(
        'partials/_top_recruiters',
        [
            'topRecruiters' => $topRecruiters,
            'topRecruitersSubText' => $topRecruitersSubText,
        ]
    ); ?>
<?php endif; ?>

<!-- Our Campuses -->
<?php if (!empty($campusColleges)): ?>
    <?= $this->render(
        'partials/_our-campus',
        [
            'campusColleges' => $campusColleges,
            'ourCampusesSubText' => $ourCampusesSubText,
            'collegeName' => $college->display_name ?? $college->name,
            'template' => CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO
        ]
    ); ?>
<?php endif; ?>

<!-- About institute section -->
<?php if (!empty($college) && !empty($college->collegeContents)):
    $infoPage = null;
    foreach ($college->collegeContents as $content) {
        if ($content->sub_page === 'info') {
            $infoPage = $content;
            break;
        }
    } ?>
    <?= $this->render(
        'partials/_about_college',
        [
            'info' => $infoPage ?? [],
            'collegeBanner' => $college->cover_image,
            'collegeName' => $college->display_name ?? $college->name,
            'collegeSlug' => $college->slug,
            'template' => CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO,
            'aboutCollegeSubText' => $aboutSubText ?? []
        ]
    ); ?>
<?php endif; ?>

<?= $this->render('partials/_college_footer_section') ?>

<!-- modal start -->
<div class="modal-overlay-box" id="form-modal">
    <div class="modal-body">
        <div id="modal-close">&times;</div>
        <div class="modal-head">
            Admissions Open for 2025
            <span>Hurry, Limited Seats Available</span>
        </div>
        <?= $this->render('partials/_modal_college_based_cta_form', [
            'college' => $college,
            'clp' => $clp,
            'streams' => $stream,
            'level' => $level,
            'city' => $city,
            'states' => $states,
            'courses' => $courses,
            'programsList' => $programsList,
        ]) ?>
    </div>
</div>

<?php $this->registerJsFile(Yii::$app->params['jsPath'] . 'slick-slick.min.js', ['depends' => [AppAsset::class], 'defer' => true]); ?>
<?php $this->registerJsFile(Yii::$app->params['jsPath'] . 'clp_script.js', ['depends' => [AppAsset::class], 'defer' => true]); ?>