<?php

use common\models\CustomLandingPage;
use frontend\helpers\Url;

$backgroundImage = !empty($getClpCollegeImages['banner_image']) ? Url::toClpCollegeBanner($getClpCollegeImages['banner_image'])
    : (!empty($college['cover_image']) ? Url::getCollegeBannerImage($college['cover_image']) : Url::toDefaultCollegeBanner());
?>
<section class="hero-banner" style="background-image: url('<?= $backgroundImage ?>');">
    <div class="banner-overlay"></div>
    <div class="container">
        <div class="banner-left-content">
            <h1><?= !empty($clp->page_title) ? $clp->page_title : ($college['name'] ?? '') ?></h1>
            <div class="banner-liner"><?= !empty($clp->page_sub_title) ? $clp->page_sub_title : 'Experience a Supportive and Inspiring Educational Environment ' ?></div>
            <?php if ($template == CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO && empty($clp->page_description)): ?>
                <ul>
                    <li><span class="stream"><img src="/yas/images/clp/college-based-two/usp-icon1-color.png" alt="usps"></span> 275+ Prominent Recruiters</li>
                    <li><span class="stream"><img src="/yas/images/clp/college-based-two/usp-icon2-color.png" alt="usps"></span> 15000+ Alumni Network</li>
                    <li><span class="stream"><img src="/yas/images/clp/college-based-two/usp-icon3-color.png" alt="usps"></span> AICTE+ Approved PGDM Programmes</li>
                    <li><span class="stream"><img src="/yas/images/clp/college-based-two/usp-icon4-color.png" alt="usps"></span> NAAC A+ Accredited Institute</li>
                </ul>
            <?php elseif ($template == CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO && !empty($clp->page_description)): ?>
                <?= $clp->page_description ?? '' ?>
            <?php endif; ?>
        </div>
        <div class="banner-right-content modal-body">
            <div class="modal-head">
                <?= !empty($clp->form_title) ? $clp->form_title : 'Admissions Open for 2025' ?>
                <span><?= !empty($clp->form_description) ? $clp->form_description : 'Hurry, Limited Seats Available' ?></span>
            </div>
            <form id="genericfirstScreen">
                <div class="form-content">
                    <div class="form-field-block">
                        <input type="text" placeholder="Name*" id="name" name="name" autocomplete="off" class="required-field field-style">
                        <span class="form-error" id="nameError2"></span>
                    </div>
                    <div class="form-field-block">
                        <input type="email" placeholder="Email*" id="email" name="email" autocomplete="off" class="required-field field-style">
                        <span class="form-error" id="emailError"></span>
                    </div>
                    <div class="form-field-block">
                        <input type="text" placeholder="Phone number*" id="phone" maxlength="10" name="phone" autocomplete="off" class="required-field field-style">
                        <span class="form-error" id="phoneError"></span>
                    </div>

                    <?= $this->render('_college_based_select_form_fields', [
                        'clp' => $clp,
                        'streams' => $streams,
                        'city' => $city,
                        'states' => $states,
                        'courses' => $courses,
                        'programsList' => $programsList,
                        'level' => $level,
                        'college' => $college,
                        'campus' => $campus ?? [],
                        'type' => 'banner'
                    ]) ?>

                    <div class="form-field-block">
                        <button type="submit" class="modal-button" id="genericScreenSubmit" disabled><?= !empty($clp->form_cta_text) ? $clp->form_cta_text : 'Apply Now' ?></button>
                    </div>
                </div>
                <input type="hidden" id="template_id" name="template_id" value="<?= $clp->template_id ?>">
                <input type="hidden" name="slug" value="<?= $clp->slug ?>">
                <input type="hidden" name="form_cta_text" value="<?= $clp->form_cta_text ?? 'Apply Now' ?>">
                <input type="hidden" id="college_id" name="college_id" value="<?= $college->id ?? '' ?>">
                <input type="hidden" id="college_slug" value="<?= $college->slug ?? '' ?>">
                <input type="hidden" id="redirection_url" value="<?= $clp->redirection_url ?? '' ?>">
                <input type="hidden" name="_csrf-frontend" value="<?= Yii::$app->request->csrfToken; ?>">
                <input type="hidden" name="utm_source" value="<?= !empty($_GET['utm_source']) ? $_GET['utm_source'] : '' ?>">
                <input type="hidden" name="utm_medium" value="<?= !empty($_GET['utm_medium']) ? $_GET['utm_medium'] : '' ?>">
                <input type="hidden" name="utm_campaign" value="<?= !empty($_GET['utm_campaign']) ? $_GET['utm_campaign'] : '' ?>">
            </form>
        </div>
    </div>
</section>