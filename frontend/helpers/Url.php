<?php

namespace frontend\helpers;

use common\helpers\DataHelper;
use Yii;
use yii\helpers\Url as HelpersUrl;

class Url extends HelpersUrl
{
    /**
     * Undocumented function
     *
     * @return void
     */
    public static function toExam()
    {
        $lang_code = Yii::$app->language;
        if ($lang_code == 'en') {
            $lang_code = '';
        }
        return (empty($lang_code)) ? self::toRoute(['exam/home']) : self::to(['exam/home', 'lang' => $lang_code]);
    }

    /**
     * Return exam detail page URL
     *
     * @param string $slug
     * @param string $pageSlug
     * @return string
     */
    public static function toExamDetail($slug, $lang_code = '', $pageSlug = null, $cache = false)
    {
        if (!empty($lang_code) && $lang_code != 'en') {
            $params['lang'] = $lang_code;
        }
        $params['exam'] = $slug;


        if (!empty($pageSlug)) {
            $params['page'] = $pageSlug;
        }

        if ($cache == false) {
            return self::toRoute(array_merge(['exam/detail'], $params));
        } else {
            $urlPage = (!empty($params['page']) && $params['page'] == 'overview') ? $params['exam'] : $params['exam'] . '-' . $params['page'];
            return !empty($params['lang']) ? Url::base(true) . '/' . $params['lang'] . '/exams/' . $urlPage : Url::base(true) . '/exams/' . $urlPage;
        }
    }



    public static function toExamDetailSubPage($slug, $pageSlug = null, $dropDown = null, $dropSlug = null, $lang_code = '')
    {
        if (!empty($lang_code) && $lang_code != 'en') {
            if ($pageSlug == 'syllabus') {
                return Url::base(true) . '/' . $lang_code . '/exams/' . $slug . '-' . $dropSlug . '-syllabus';
            } else if (($pageSlug == 'cut-off') && ($dropSlug != 'qualifying-marks')) {
                return Url::base(true) . '/' . $lang_code . '/exams/' . $slug . '-' . $pageSlug . '-' . $dropSlug;
            } else {
                return Url::base(true) . '/' . $lang_code . '/exams/' . $slug . '-' . $dropSlug;
            }
        } else {
            if ($pageSlug == 'syllabus') {
                return Url::base(true) . '/exams/' . $slug . '-' . $dropSlug . '-syllabus';
            } else if (($pageSlug == 'cut-off') && ($dropSlug != 'qualifying-marks')) {
                return Url::base(true) . '/exams/' . $slug . '-' . $pageSlug . '-' . $dropSlug;
            } else {
                return Url::base(true) . '/exams/' . $slug . '-' . $dropSlug;
            }
        }
    }


    /**
     * Return exam list by discipline and location
     *
     * @param string $slug discipline slug
     * @param string $location
     * @return string
     */
    public static function toDisciplineExam($slug, $location = 'india', $lang_code = '')
    {
        if ($lang_code == 'en') {
            $lang_code = '';
        }
        return (empty($lang_code)) ? self::to(['exam/filter', 'discipline' => $slug, 'location' => $location]) : self::to(['exam/filter', 'discipline' => $slug, 'location' => $location, 'lang' => $lang_code]);
    }



    /**
     * Return exam articles list
     *
     * @return string
     */
    public static function toArticles($lang_code = '')
    {
        if ($lang_code == 'en') {
            $lang_code = '';
        }
        return (empty($lang_code)) ? self::to(['article/index']) : self::to(['article/index', 'lang' => $lang_code]);
    }

    /**
     * Article detail page
     *
     * @return string article slug
     */
    public static function toArticleDetail($slug, $lang_code = '')
    {
        $urlExpload = explode('/', \Yii::$app->request->getUrl());
        if (isset($urlExpload[1]) && $urlExpload[1] == 'amp') {
            $lang_code_amp = '';
            if ($lang_code == 'hi') {
                $lang_code_amp = 'hi' . '/';
            }
            return  Url::base(true) . '/amp/' . $lang_code_amp . 'articles/' . $slug;
            //return (empty($lang_code)) ? self::to(['article/route', 'slug' => $slug]) : self::to(['article/route', 'lang' => $lang_code, 'slug' => $slug]);
        }

        if ($lang_code == 'en') {
            $lang_code = '';
        }
        return (empty($lang_code)) ? self::to(['article/route', 'slug' => $slug]) : self::to(['article/route', 'lang' => $lang_code, 'slug' => $slug]);
    }

    /**
     * Return exam ncert list
     *
     * @return string
     */
    public static function toNcert()
    {
        return self::to(['ncert/index']);
    }

    /**
     * Ncert detail page
     *
     * @return string ncert slug
     */
    public static function toNcertDetail($slug)
    {
        return self::to(['ncert/route', 'slug' => $slug]);
    }

    /**
     * Return Study Abroad Articles List
     *
     * @return string
     */
    public static function toStudyAbroadArticles($countrySlug)
    {
        return self::to(['article/study-abroad-index', 'country' => $countrySlug]);
    }

    /**
     * Study Abroad Article Detail Page
     *
     * @return string
     */
    public static function toCountryDetail($country, $slug)
    {
        return self::to(['article/study-abroad-detail', 'country' => $country, 'slug' => $slug]);
    }

    /**
     * Study Abroad Detail Page
     *
     * @return string
     */
    public static function toSaCollegeDetail($country, $slug, $page = 'overview')
    {
        if ($page == 'overview') {
            return Url::base(true) . '/' . $country . '/university/' . $slug;
        } else {
            return Url::base(true) . '/' . $country . '/university/' . $slug . '/' . $page;
        }
    }

    /**
     * Study Abroad Landing Page
     *
     * @return string
     */
    public static function toSaCountry($countrySlug)
    {
        return Url::base(true) . '/' . $countrySlug;
    }

    /**
     * Get Study abroad image
     *
     * @return string
     */
    public static function getStudyAbroadImage($image = null)
    {
        $coverImage = DataHelper::s3Path(null, 'article_study_abroad', 'path') . '/' . $image;

        // return !empty($image) ? $coverImage : '/yas/images/defaultcardbanner.png';
        return !empty($image) ? $coverImage : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
    }

    /**
     * Get Study abroad image
     *
     * @return string
     */
    public static function getStudyAbroadCollegeBannerImage($image = null)
    {
        $coverImage = DataHelper::s3Path(null, 'college_banner_study_abroad', 'path') . '/' . $image;

        return !empty($image) ? $coverImage : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
    }

    /**
     * Get Study abroad image
     *
     * @return string
     */
    public static function getStudyAbroadCollegeLogoImage($image = null)
    {
        $coverImage = DataHelper::s3Path(null, 'college_logo_study_abroad', 'path') . '/' . $image;

        return !empty($image) ? $coverImage : 'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
    }


    /**
     * News landing page
     *
     * @return string article slug
     */
    public static function toNews($lang_code = '')
    {
        if ($lang_code == 'en') {
            $lang_code = '';
        }
        return (empty($lang_code)) ? self::to(['news/index']) : self::to(['news/index', 'lang' => $lang_code]);
    }

    /**
     * News detail page
     *
     * @return string article slug
     */
    public static function toNewsDetail($slug, $lang_code = '')
    {
        $urlExpload = explode('/', \Yii::$app->request->getUrl());

        if ($lang_code == 'en') {
            $lang_code = '';
        }
        if (Yii::$app->controller->id !== 'news') {
            if (isset($urlExpload[1]) && $urlExpload[1] == 'amp') {
                return  Url::base(true) . '/amp/' . $slug;
            }

            if (empty($lang_code)) {
                return self::toNewGetmyuni() . $slug;
            } else {
                return self::toNewGetmyuni() . $lang_code . '/' . $slug;
            }
        } else {
            if (isset($urlExpload[1]) && $urlExpload[1] == 'amp') {
                return  Url::base(true) . '/amp/news/' . $slug;
            }
            return (empty($lang_code) ? self::to(['news/route', 'slug' => $slug]) : self::to(['news/route', 'lang' => $lang_code, 'slug' => $slug]));
        }
        // return (empty($lang_code) ? self::to(['news/route', 'slug' => $slug]) : self::to(['news/route', 'lang' => $lang_code, 'slug' => $slug]));
    }

    public static function toNewsImages($image = null)
    {
        if (empty($image)) {
            // return '/yas/images/defaultcardbanner.png';
            return 'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
        }
        $url = DataHelper::s3Path(null, 'news_genral', 'path');
        return $url . '/' . $image;
    }

    public static function toAmp($slug)
    {
        return self::toRoute('/amp' . $slug, true);
    }

    /**
     * Boards landing page
     *
     * @return string
     */
    public static function toBoards($lang_code = '')
    {
        if ($lang_code == 'en') {
            $lang_code = '';
        }
        return (empty($lang_code) ? self::to(['board/index']) : self::to(['board/index', 'lang' => $lang_code]));
    }

    /**
     * Board detail page
     *
     * @return string
     */
    public static function toBoardDetail($slug, $lang_code = '', $pageSlug = null, $cache = false)
    {

        if (!empty($lang_code)) {
            $params['lang'] = $lang_code;
        }
        $params['board'] = $slug;

        if (!empty($pageSlug)) {
            $params['page'] = $pageSlug;
        }
        if ($cache == false) {
            return self::toRoute(array_merge(['board/detail'], $params));
        } else {
            $urlPage = (!empty($params['page']) && $params['page'] == 'overview') ? $params['board'] : $params['board'] . '/' . $params['page'];
            return !empty($params['lang']) ? Url::base(true) . '/' . 'boards/' . $params['lang'] . '/' . $urlPage : Url::base(true) . '-' . $urlPage;
        }
        $urlSlug = 'boards/' . $entitySlug . '-' . $dropDownPage;
    }

    public static function toBoardDetailSubPage($slug, $pageSlug = null, $dropDown = null, $lang_code = '', $cache = false)
    {
        if (!empty($lang_code)) {
            $params['lang'] = $lang_code;
        }
        $params['board'] = $slug;

        if (!empty($dropDown)) {
            // if ($pageSlug == 'supplementary') {
            //     $params['subject'] = $dropDown . '-smy';
            // } else {
            $params['subject'] = $dropDown;
            $params['page'] = $pageSlug;
            // }
        }

        if (!empty($pageSlug) && empty($dropDown)) {
            $params['page'] = $pageSlug;
        }
        if ($cache == false) {
            return self::toRoute(array_merge(['board/detail'], $params));
        } else {
            $urlPage = ((!empty($params['subject'])) && ($pageSlug == 'supplementary')) ? $pageSlug . '-' . $params['subject'] : $params['subject'] . '-' . $pageSlug;
            return !empty($params['lang']) ? Url::base(true) . '/' . 'boards/' . $params['lang'] . '/' . $params['board'] . '/' . $urlPage : Url::base(true) . '/' . 'boards/' . $params['board'] . '-' . $urlPage;
        }
    }

    /**
     * Board sample paper detail page
     *
     * @return string
     */
    public static function toBoardSamplePaperDetail($pageSlug)
    {
        return self::to(['board/sample-paper', 'boardSamplePaperSlug' => $pageSlug]);
    }

    /**
     * Board sample paper set qustion paper
     *
     * @return string
     */
    public static function toBoardSampleQuestion($url)
    {
        return DataHelper::s3Path(null, 'board_papers', 'path') . '/' . $url;
    }

    /**
     * Get Board Image
     *
     * @return string
     */
    public static function getBoardImage($image)
    {
        return DataHelper::s3Path(null, 'board', 'path') . '/' . $image;
    }

    /**
     * Return exam cover image url
     *
     * @param string $imageName cover_image
     * @return string
     */
    public static function toExamImage($imageName = null)
    {
        if (!empty($imageName)) {
            $url = DataHelper::s3Path(null, 'exam_genral', 'path');
            return $url . '/' . $imageName;
        } else {
            return 'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
        }
    }

    /**
     * Returns author detail with posts
     *
     * @param int $id Author Id
     **/
    public static function toAllAuthorPost($slug, $lang_code = '')
    {
        return (empty($lang_code)) ? self::to(['site/author-profile', 'slug' => $slug]) : self::to(['site/author-profile', 'lang' => $lang_code, 'slug' => $slug]);
    }

    public static function toTag($slug)
    {
        return self::to(['article/tag', 'slug' => $slug]);
    }

    public static function toDefaultImage()
    {
        // return '/yas/images/defaultcardbanner.png';
        return 'https://media.getmyuni.com/yas/images/defaultcardbanner.png';
    }

    public static function toWpComment()
    {
        return self::to(['/ajax/wp-comment']);
    }

    public static function toSitemap()
    {
        return self::to('sitemap/index');
    }

    public static function toCanonical($page = null)
    {
        $url = strtr('scheme://hostpath', parse_url(parent::canonical()));

        if ($page && $page > 1) {
            $url .= '?page=' . $page;
        }

        return $url;
    }

    /**
     * College landing page
     *
     * @return string https://<domain>/college/<slug>
     */
    public static function toCollege($slug, $page = '', $program = '', $activeIndex = '', $cache = false)
    {
        $params = ['slug' => $slug];
        if (!empty($page)) {
            if ($page == 'courses-fees') {
                $params = ['slug' => $slug . '-' . $page];
                if ($program) {
                    if (!empty($activeIndex) && $activeIndex == 1) {
                        return ($cache == false) ? Url::to(['college/' . $slug . '-' . $page . '/' . $program]) : Url::base(true) . '/college/' . $params['slug'] . '?program=' . $program . '-pi';
                        // $params = ['slug' => $slug . '-' . $page. '/'. $program];
                    } else {
                        $params['program'] = $program;
                    }
                }
            } else if ($page == 'reviews' || $page == 'news') {
                return ($cache == false) ? Url::to(['college/' . $slug . '/' . $page]) : Url::base(true) . '/college/' . $slug . '/' . $page;
            } else if ($page == 'compare-college') {
                return Url::to(['college/' . $slug . '/' . $page]);
            } else if ($page == 'qna') {
                return Url::toQna($slug);
            } else {
                $params['page'] = $page;
            }
        }
        if ($cache == false) {
            return self::toRoute(array_merge(['college/index'], $params), true);
        } else {
            $params = (!empty($page) && ($page !== 'info')) ? ['slug' => $slug . '-' . $page] : ['slug' => $slug];
            return Url::base(true) . '/college/' . $params['slug'];
        }
    }

    public static function toCollegeDetailSubPage($slug, $pageSlug = null, $dropDown = null, $dropSlug = null)
    {
        $params = ['slug' => $slug];

        if ($dropDown == 'UG' || $dropDown == 'PG') {
            return Url::base(true) . '/college/' . $slug . '-' . strtolower($dropDown) . '-' . $pageSlug;
        } else {
            if ($pageSlug == 'syllabus' || $pageSlug == 'cut-off') {
                return Url::base(true) . '/college/' . $slug . '-' . $pageSlug . '/' . $dropSlug;
            }
        }
    }

    public static function toDomain()
    {
        return \Yii::$app->params['siteUrl'];
    }

    public static function toCompareCollege($slug)
    {
        return 'college-compare?college1=' . $slug;
    }

    public static function toClpCollegeLogo($logo)
    {
        return 'https://media.getmyuni.com/assets/clp/college/logo/' . $logo;
    }

    public static function toClpCollegeBanner($banner)
    {
        return 'https://media.getmyuni.com/assets/clp/college/banner/' . $banner;
    }

    /**
     * Get college logo image
     *
     * @return string
     */
    public static function getCollegeLogo($logo)
    {
        return 'https://media.getmyuni.com/azure/college-image/small/' . $logo;
    }

    public static function getCollegeBannerImage($banner)
    {
        return 'https://media.getmyuni.com/azure/college-image/big/' . $banner;
    }

    public static function defaultCollegeLogo()
    {
        return 'https://media.getmyuni.com/yas/images/gmu-icon.svg';
    }

    public static function toDefaultCollegeBanner()
    {
        // return  Url::base(true) . '/yas/images/banner.png';
        return 'https://media.getmyuni.com/yas/images/banner.png';
    }


    /**
     * Get college image
     *
     * @return string
     */
    public static function getCollegeImage($slug, $image)
    {
        return DataHelper::s3Path($image, 'gallery', true, $slug);
    }

    /**
     * User Review Page URl
     * @return string
     */

    public static function toUserReviewUrl($userSlug, $collegeSlug)
    {
        return self::toDomain() . 'reviews/' . $userSlug . '-review-on-' . $collegeSlug;
    }

    public static function hasParameters()
    {
        $page = Yii::$app->request->get('page');

        if ($page && $page > 0) {
            return true;
        }

        return false;
    }

    /**
     * URL for College Course Ci pages.
     * @return Url
     */
    public static function toCollegeCourse($slug, $collegeSlug, $pageIndex = null)
    {
        if (!empty($pageIndex) && $pageIndex == 1) {
            return self::to(['college/course', 'slug' => $collegeSlug, 'courseSlug' => $slug]);
        }

        return self::toDomain() . 'college/' . $collegeSlug . '-courses-fees?course=' . $slug;
    }

    /**
     * URL for College Course PLP pages.
     * @param $slug  Program Page Slug with Id., $collegeSlug College Slug
     * @return string
     */
    public static function toCollegeCourseProgram($collegeSlug, $slug, $pageIndex = null)
    {
        if (!empty($pageIndex) && $pageIndex == 1) {
            return self::to(['college/program', 'slug' => $collegeSlug, 'programSlug' => $slug]);
        }

        return self::toCollege($collegeSlug, 'courses-fees', $slug . '-pi');
    }

    /**
     * College Broucher URL
     */
    public static function toCollegeBroucher($param)
    {
        return DataHelper::s3Path(null, 'brochure', 'path') . '/' . $param;
    }

    //will update once course done
    public static function toAllCollege($url)
    {
        return self::toDomain() . 'all-colleges/' . $url;
    }

    //will update once course done
    public static function toDisciplineCollege($discipline, $state = '')
    {
        if (!empty($state)) {
            return self::toDomain() . $discipline . '-colleges/' . $state;
        }
        return self::toDomain() . $discipline . '-colleges';
    }

    public static function toCourse()
    {
        return self::toRoute(['course/index']);
    }

    public static function toCourseDetail($slug, $pageSlug = '', $cache = false)
    {
        $params = ['course' => $slug];

        if (!empty($pageSlug)) {
            $params['page'] = $pageSlug;
        }
        if ($cache == false) {
            return self::toRoute(array_merge(['course/detail'], $params));
        } else {
            return ($pageSlug == 'about') ? Url::base(true) . '/' . $params['course'] . '-course' : Url::base(true) . '/' . $params['course'] . '-' . $params['page'];
        }
    }

    public static function toCourseDetailSubPage($slug, $pageSlug = null, $dropDown = null, $cache = false)
    {
        $params = ['course' => $slug];

        if (!empty($dropDown)) {
            $final = preg_replace('#[ -]+#', '-', $dropDown);
            $params['sub'] = strtolower($final);
            // if ($pageSlug == 'syllabus-subjects') {

            // } else {
            //     $params['sub'] = $final;
            // }
        }
        $params['page'] = $pageSlug;
        if ($cache == false) {
            return self::toRoute(array_merge(['course/detail'], $params));
        } else {
            return Url::base(true) . '/' . $params['course'] . '-' . $params['page'] . '/' . $params['sub'];
        }
    }

    public static function toCoursesStream($url)
    {
        return self::toRoute('courses/' . $url);
    }

    public static function toCourseFilter($url)
    {
        return self::toDomain() . $url;
    }

    public static function toQna($url)
    {
        return self::toDomain() . 'college/' . $url . '/qna';
    }

    //dynamic-cta pdf download url path
    public static function toDynamicPdf($pdf)
    {
        return Yii::getAlias('@dynamicCtaFileFrontend') . '/' . $pdf;
    }

    // filter page urls
    public static function toAllColleges($location = null)
    {
        if ($location) {
            return self::toRoute('college/all-colleges', ['location' => $location], true);
        }

        return self::toRoute('college/all-colleges');
    }

    public static function toFilterPage($filter, $location = null)
    {
        if ($location) {
            return self::toRoute('college/college-filter', ['filter' => $filter, 'location' => $location]);
        }

        return self::toRoute('college/college-filter', ['filter' => $filter]);
    }

    public static function toTestUrl()
    {
        return Url::base(true) . '/';
    }

    /**
     * Get review student image
     *
     * @return string
     */
    public static function getReviewImage($logo)
    {
        return '/yas/images/review-images/' . $logo;
    }

    /**
     * Get review student default image
     *
     * @return string
     */
    public static function getDefaultReviewImage()
    {
        return '/yas/images/defaultReviewImage.png';
    }

    public static function toQnaDetail($entity, $slug)
    {
        return self::toRoute('q-n-a/' . $slug);
        // $entity = ($entity == 'exam') ? 'exams' : $entity;
        // return self::toRoute($entity . '/qna-details/' . $slug);
    }
    public static function toQnaLandingPage($entity, $slug)
    {
        return self::toRoute($entity . '/' . $slug . '/questions');
    }

    /**
     * Career detail page
     *
     * @return string  slug
     */
    public static function toCareerDetail($slug, $pageSlug = '', $cache = false)
    {
        $params = ['career' => $slug];

        if (!empty($pageSlug)) {
            $params['page'] = $pageSlug;
        }

        if ($cache == false) {
            return self::toRoute(array_merge(['career/detail'], $params));
        } else {
            return (!empty($params['page']) && $params['page'] == 'overview') ? Url::base(true) . '/careers/' . $params['career'] : Url::base(true) . '/careers/' . $params['career'] . '-' . $params['page'];
        }
    }

    /**
     * Return Career list
     *
     * @return string
     */
    public static function toCareers()
    {
        return self::to(['career/index']);
    }

    /**
     * Return Career Category
     *
     * @return string
     */
    public static function toCareerCategory($slug)
    {
        return self::to(['career/category', 'slug' => $slug]);
    }

    public static function toGetmyuni()
    {
        return 'https://www.getmyuni.com/';
        // return 'http://495541ad.thegmu.com/';
        // return 'http://yii-frontend.gmu.org/';
    }

    public static function toNewGetmyuni()
    {
        return 'https://news.getmyuni.com/';
    }

    /**
     * Return Career list
     *
     * @return string
     */
    public static function toScholarships()
    {
        return self::to(['scholarship/index']);
    }

    /**
     * Return Scholarship Category
     *
     * @return string
     */
    public static function toScholarshipCategory($slug)
    {
        return self::to(['scholarship/category', 'slug' => $slug]);
    }

    /**
     * Scholarship detail page
     *
     * @return string  slug
     */
    public static function toScholarshipDetail($slug, $pageSlug = '', $cache = false)
    {
        $params = ['scholarship' => $slug];

        if (!empty($pageSlug)) {
            $params['page'] = $pageSlug;
        }

        if ($cache == false) {
            return self::toRoute(array_merge(['scholarship/detail'], $params));
        } else {
            return (!empty($params['page']) && $params['page'] == 'overview') ? Url::base(true) . '/scholarship/' . $params['scholarship'] : Url::base(true) . '/scholarship/' . $params['scholarship'] . '-' . $params['page'];
        }
    }

    public static function toOlympiadDetail($slug, $pageSlug = '', $cache = false)
    {
        $params = [
            'olympiad' => $slug,
            'page' => ''
        ];
        if (!empty($pageSlug)) {
            $params['page'] = $pageSlug;
        }
        if ($cache == false) {
            return self::toRoute(array_merge(['olympiad/detail'], $params));
        } else {
            return (!empty($params['page']) && $params['page'] == 'overview') ? Url::base(true) . '/olympiad/' . $params['olympiad'] : Url::base(true) . '/olympiad/' . $params['olympiad'] . '-' . $params['page'];
        }
    }

    public static function toBridgeU()
    {
        return 'https://indiacolleges.bridge-u.com/';
        // return 'https://www.getmyuni.com/';
    }

    /**
     * Return exam articles list
     *
     * @return string
     */
    public static function toGetgisArticles()
    {
        return self::to(['getgis-article/index']);
    }

    /**
     * Article detail page
     *
     * @return string article slug
     */
    public static function toGetgisArticleDetail($slug)
    {
        return self::to(['getgis-article/detail', 'slug' => $slug]);
    }

    public static function toGetGisLink($url)
    {
        return \Yii::$app->params['GetGisUrl'] . $url;
    }

    public static function toPrCalculator()
    {
        return self::toRoute(['calculator/pr-calculator']);
    }

    public static function toSaskatchewanCalculator()
    {
        return self::toRoute(['calculator/saskatchewan-calculator']);
    }

    public static function toClbConverter()
    {
        return self::toRoute(['calculator/clb-converter']);
    }

    public static function toCrsCalculator()
    {
        return self::toRoute(['calculator/crs-calculator']);
    }

    public static function toAusCalculator()
    {
        return self::toRoute(['calculator/australia-pr-points-calculator']);
    }

    public static function toGermanyCalculator()
    {
        return self::toRoute(['calculator/germany-opp-calculator']);
    }

    public static function toAustraliaLink()
    {
        return self::toRoute(['immigration/pr-permanent-residence/australia']);
    }

    public static function toCanadaLink()
    {
        return self::toRoute(['immigration/pr-permanent-residence/canada']);
    }

    public static function toAustriaLink()
    {
        return self::toRoute(['immigration/job-seeker-visa/austria']);
    }

    public static function toGermanyOppLink()
    {
        return self::toRoute(['immigration/job-seeker-visa/germany']);
    }

    public static function toSwedenLink()
    {
        return self::toRoute(['immigration/job-seeker-visa/sweden']);
    }

    public static function toUaeLink()
    {
        return self::toRoute(['immigration/job-seeker-visa/uae']);
    }

    public static function toImmigrationLink()
    {
        return self::toRoute(['immigration/index']);
    }

    public static function toPRLink()
    {
        return self::toRoute(['immigration/pr-permanent-residence']);
    }

    public static function toJobSeekerLink()
    {
        return self::toRoute(['immigration/job-seeker-visa']);
    }

    public static function toClpTopRecruiterLogo($image)
    {
        return 'https://media.getmyuni.com/assets/clp/recruiter_logos/' . $image;
    }
}
