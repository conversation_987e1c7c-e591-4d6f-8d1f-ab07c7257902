<?php

namespace common\services;

use common\models\City;
use common\models\ClpCollegeImagePanel;
use common\models\ClpCollegeUsp;
use common\models\College;
use common\models\CollegeProgram;
use common\models\Course;
use common\models\CustomLandingPage;
use common\models\Degree;
use common\models\Program;
use common\models\State;
use common\models\Stream;
use common\models\TopRecruiters;
use yii\db\Query;

class ClpService
{
    public function getCLPData($slug)
    {
        $clpData = CustomLandingPage::find()->where(['slug' => $slug])->one();
        return $clpData;
    }

    public function getStreams($streamIds = [], $limit = 10, $templateId = null, $collegeId = null, $search = null)
    {
        $streams = [];

        // Case: No stream IDs provided but valid college & template inputs
        if (empty($streamIds)) {
            if ($templateId !== CustomLandingPage::TEMPLATE_GENERIC && !empty($collegeId)) {
                $query = new Query();
                $query->select(['stream.name', 'stream.id'])
                    ->distinct()
                    ->from([CollegeProgram::tableName() . ' as cp'])
                    ->leftJoin(Course::tableName() . ' as course', 'course.id = cp.course_id')
                    ->leftJoin(Stream::tableName() . ' as stream', 'stream.id = course.stream_id')
                    ->where(['cp.college_id' => $collegeId])
                    ->andWhere(['not', ['stream.slug' => 'other']])
                    ->andWhere(['cp.status' => CollegeProgram::STATUS_ACTIVE])
                    ->andWhere(['not', ['stream.id' => null]]);

                if (!empty($search)) {
                    $query->andWhere(['like', 'stream.name', $search]);
                }

                $streams = $query->all();
            }

            return $streams ?? [];
        }

        // Case: streamIds are present
        $query = Stream::find()->select(['id', 'name'])->asArray();

        if (!empty($search)) {
            $query->where(['in', 'id', $streamIds])
                ->andWhere(['like', 'name', $search]);
        } else {
            $query->where(['in', 'id', $streamIds]);
        }

        $streams = $query->all();

        return $streams;
    }

    public function getDegrees($degreeIds = [], $limit = 10, $templateId = null, $collegeId = null, $search = null, $streamId = null)
    {
        $degrees = [];
        // Case: No degree IDs provided but valid college & template inputs
        if (empty($degreeIds)) {
            if ($templateId !== CustomLandingPage::TEMPLATE_GENERIC && !empty($collegeId)) {
                $query = new Query();
                $query->select(['degree.id', 'degree.name', 'stream_id'])
                    ->distinct()
                    ->from([CollegeProgram::tableName() . ' as cp'])
                    ->leftJoin(Course::tableName(), 'course.id = cp.course_id')
                    ->leftJoin('degree', 'degree.slug = course.degree')
                    ->where(['stream_id' => $streamId])
                    ->andWhere(['cp.college_id' => $collegeId])
                    ->andWhere(['not', ['degree.slug' => 'other']])
                    ->andWhere(['degree.status' => Degree::STATUS_ACTIVE])
                    ->orderBy(['degree.id' => SORT_ASC]);

                if (!empty($search)) {
                    $query->andWhere(['like', 'degree.name', $search]);
                }

                $degrees = $query->all();
            }

            return $degrees ?? [];
        }

        // Case: degreeIds are present
        $query = Degree::find()->select(['id', 'name'])->asArray();

        if (!empty($search)) {
            $query->where(['in', 'id', $degreeIds])
                ->andWhere(['like', 'name', $search]);
        } else {
            $query->where(['in', 'id', $degreeIds]);
        }

        $degrees = $query->all();

        return $degrees;
    }

    public function getCity($cityIds = [], $search = null)
    {
        if (empty($cityIds)) {
            return [];
        }

        $query = City::find()->select(['id', 'name'])->asArray();

        if (!empty($cityIds) && empty($search)) {
            $query->where(['in', 'id', $cityIds]);
        } elseif (!empty($search) && !empty($cityIds)) {
            $query->where(['in', 'id', $cityIds]);
            $query->andWhere(['like', 'name', $search]);
        }

        $city = $query->all();

        return $city;
    }

    public function getState($stateIds = [], $search = null)
    {
        if (empty($stateIds)) {
            return [];
        }

        $query = State::find()->select(['id', 'name'])->asArray();

        if (!empty($stateIds) && empty($search)) {
            $query->where(['in', 'id', $stateIds]);
        } elseif (!empty($search) && !empty($stateIds)) {
            $query->where(['in', 'id', $stateIds]);
            $query->andWhere(['like', 'name', $search]);
        }

        $states = $query->all();

        return $states;
    }

    public function getCourses($courseIds = [], $templateId = null, $collegeId = null, $search = null)
    {
        if (empty($courseIds)) {
            if ($templateId !== CustomLandingPage::TEMPLATE_GENERIC && !empty($collegeId)) {
                $query = new Query();
                $query->select(['c.id', 'c.name'])
                    ->from(CollegeProgram::tableName() . ' as cp')
                    ->leftJoin(Course::tableName() . ' as c', 'c.id = cp.course_id')
                    ->where(['cp.college_id' => $collegeId])
                    ->andWhere(['cp.status' => CollegeProgram::STATUS_ACTIVE])
                    ->groupBy('c.id')
                    ->orderBy(['c.id' => SORT_DESC]);

                if (!empty($search)) {
                    $query->andWhere(['like', 'c.name', $search]);
                }

                $courses = $query->all();
            }

            return $courses ?? [];
        }

        $query = Course::find()->select(['id', 'name'])->asArray();

        if (!empty($courseIds) && empty($search)) {
            $query->where(['in', 'id', $courseIds]);
        } elseif (!empty($search) && !empty($courseIds)) {
            $query->where(['in', 'id', $courseIds]);
            $query->andWhere(['like', 'name', $search]);
        }

        $courses = $query->all();

        return $courses;
    }

    public function getCampus($campusIds = [], $search = null)
    {
        if (empty($campusIds)) {
            return [];
        }

        $query = College::find()->select(['id', 'name'])->asArray();

        if (!empty($campusIds) && empty($search)) {
            $query->where(['in', 'id', $campusIds]);
        } elseif (!empty($search) && !empty($campusIds)) {
            $query->where(['in', 'id', $campusIds]);
            $query->andWhere(['like', 'name', $search]);
        }

        $campus = $query->all();

        return $campus;
    }

    public function getProgramsList($programIds = [], $templateId = null, $collegeId = null, $search = null)
    {
        $templates = [
            CustomLandingPage::TEMPLATE_GENERIC,
            CustomLandingPage::TEMPLATE_GENERIC_ONE,
            CustomLandingPage::TEMPLATE_GENERIC_TWO,
            CustomLandingPage::TEMPLATE_GENERIC_THREE,
        ];

        if (empty($programIds)) {
            if (!in_array($templateId, $templates) && !empty($collegeId)) {
                $query = Program::find()
                    ->distinct()
                    ->select(['program.id', 'program.name'])
                    ->leftJoin(CollegeProgram::tableName() . ' as cp', 'cp.program_id = program.id')
                    ->where(['cp.college_id' => $collegeId])
                    ->andWhere(['program.status' => Program::STATUS_ACTIVE])
                    ->groupBy('program.id')
                    ->orderBy(['program.id' => SORT_DESC]);

                if (!empty($search)) {
                    $query->andWhere(['like', 'program.name', $search]);
                }

                $programs = $query->all();
            }

            return $programs ?? [];
        }

        $query = Program::find()->distinct()->select(['id', 'name'])->asArray();

        if (!empty($programIds) && empty($search)) {
            $query->where(['in', 'id', $programIds]);
        } elseif (!empty($search) && !empty($programIds)) {
            $query->where(['in', 'id', $programIds]);
            $query->andWhere(['like', 'name', $search]);
        }

        $programs = $query->all();

        return $programs;
    }

    public function getFeaturedContent($clpId)
    {
        $clp = CustomLandingPage::find()
            ->where(['id' => $clpId])
            ->one();

        $featuredContent = $clp->featured_content;

        if (empty($featuredContent) || !is_array($featuredContent)) {
            return [];
        }

        $transformed = [];
        foreach ($featuredContent as $item) {
            if (!empty($item['fc_title'])) {
                $transformed[$item['fc_title']] = [
                    'fc_subtitle' => $item['fc_subtitle'] ?? '',
                    'fc_cta' => $item['fc_cta'] ?? '',
                ];
            }
        }

        return $transformed;
    }

    public function getProgramsWidget($programIds = [], $streamId = null, $limit = 10)
    {
        if (empty($programIds)) {
            if (!empty($streamId)) {
                $query = new Query();
                $query->select(['program.id', 'program.name'])
                    ->distinct()
                    ->from([CollegeProgram::tableName() . ' as cp'])
                    ->leftJoin(Program::tableName() . ' as program', 'program.id = cp.program_id')
                    ->leftJoin(Course::tableName() . ' as course', 'course.id = cp.course_id')
                    ->where(['course.stream_id' => $streamId])
                    ->andWhere(['cp.status' => CollegeProgram::STATUS_ACTIVE])
                    ->andWhere(['program.status' => Program::STATUS_ACTIVE])
                    ->limit($limit)
                    ->orderBy(['program.id' => SORT_ASC]);

                $programs = $query->all();
            }

            return $programs ?? [];
        }

        $query = Program::find()->select(['id', 'name'])->asArray();

        if (!empty($programIds)) {
            $query->where(['in', 'id', $programIds]);
        } else {
            $query->limit($limit);
        }

        $programs = $query->all();

        return $programs;
    }

    public function getStreamsWidget($streamIds = [], $collegeId = null, $limit = 5)
    {
        $streams = [];

        if (empty($streamIds)) {
            if (!empty($collegeId)) {
                $query = new Query();
                $query->select(['stream.name', 'stream.id'])
                    ->distinct()
                    ->from([CollegeProgram::tableName() . ' as cp'])
                    ->leftJoin(Course::tableName() . ' as course', 'course.id = cp.course_id')
                    ->leftJoin(Stream::tableName() . ' as stream', 'stream.id = course.stream_id')
                    ->where(['cp.college_id' => $collegeId])
                    ->andWhere(['not', ['stream.slug' => 'other']])
                    ->andWhere(['cp.status' => CollegeProgram::STATUS_ACTIVE])
                    ->andWhere(['not', ['stream.id' => null]])
                    ->limit($limit)
                    ->orderBy(['stream.id' => SORT_ASC]);

                $streams = $query->all();
            }

            return $streams ?? [];
        }

        $query = Stream::find()->select(['id', 'name'])->asArray();

        if (!empty($streamIds)) {
            $query->where(['in', 'id', $streamIds]);
        } else {
            $query->limit($limit);
        }

        $streams = $query->all();

        return $streams;
    }

    public function getCollege($collegeId)
    {
        $college = College::find()
            ->where(['id' => $collegeId])
            ->one();

        return $college;
    }

    public function getTopRecruiters($collegeId = null, $recruiterIds = [], $limit = 10)
    {
        $recruiters = [];

        if (!empty($collegeId) && empty($recruiterIds)) {
            $query = TopRecruiters::find()
                ->where(['college_id' => $collegeId])
                ->andWhere(['status' => TopRecruiters::STATUS_ACTIVE]);

            if (!empty($limit)) {
                $query->limit($limit);
            }

            $recruiters = $query->all();

            return $recruiters ?? [];
        }

        if (empty($recruiterIds)) {
            return [];
        }

        $query = TopRecruiters::find()
            ->where(['in', 'id', $recruiterIds])
            ->andWhere(['status' => TopRecruiters::STATUS_ACTIVE]);

        $recruiters = $query->all();

        return $recruiters;
    }

    public function getCollegeUSP($collegeId = null, $uspIds = [], $limit = 4)
    {
        $usp = [];

        if (!empty($collegeId) && empty($uspIds)) {
            $query = ClpCollegeUsp::find()
                ->where(['college_id' => $collegeId])
                ->andWhere(['status' => ClpCollegeUsp::STATUS_ACTIVE])
                ->limit($limit);


            $usp = $query->all();

            return $usp ?? [];
        }

        if (empty($uspIds)) {
            return [];
        }

        $query = ClpCollegeUsp::find()
            ->where(['in', 'id', $uspIds])
            ->andWhere(['status' => ClpCollegeUsp::STATUS_ACTIVE])
            ->limit($limit);

        $usp = $query->all();

        return $usp;
    }

    public function getCampusColleges($campusWidgetIds = [], $limit = 10)
    {
        if (empty($campusWidgetIds)) {
            return [];
        }

        $query = College::find()
            ->where(['in', 'id', $campusWidgetIds])
            ->andWhere(['status' => College::STATUS_ACTIVE]);

        if (!empty($limit)) {
            $query->limit($limit);
        }

        $colleges = $query->all();

        return $colleges;
    }

    public function getPartnerColleges($partnerCollegeWidgetIds = [], $limit = 10)
    {
        if (empty($partnerCollegeWidgetIds)) {
            $query = College::find()
                ->where(['is_sponsored' => College::SPONSORED_YES])
                ->andWhere(['status' => College::STATUS_ACTIVE]);

            if (!empty($limit)) {
                $query->limit($limit);
            }
            $colleges = $query->all();

            return $colleges ?? [];
        }

        $query = College::find()
            ->where(['in', 'id', $partnerCollegeWidgetIds])
            ->andWhere(['status' => College::STATUS_ACTIVE]);

        if (!empty($limit)) {
            $query->limit($limit);
        }

        $colleges = $query->all();

        return $colleges;
    }

    public function getClpCollegeImages($collegeId)
    {
        $collegeImages = ClpCollegeImagePanel::find()
            ->select(['logo_image', 'banner_image'])
            ->where(['college_id' => $collegeId])
            ->andWhere(['status' => ClpCollegeImagePanel::STATUS_ACTIVE])
            ->asArray()
            ->one();

        return $collegeImages;
    }
}
