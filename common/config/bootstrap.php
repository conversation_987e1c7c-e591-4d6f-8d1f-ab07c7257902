<?php

use frontend\helpers\Url;
use common\helpers\DataHelper;
use common\models\Career;
use common\models\CareerContent;
use common\models\Olympiad;
use common\models\OlympiadContent;
use common\models\Scholarship;
use common\models\ScholarshipContent;
use common\models\ScholarshipCategory;

Yii::set<PERSON>lias('@common', dirname(__DIR__));
Yii::setAlias('@api', dirname(dirname(__DIR__)) . '/api');
Yii::set<PERSON>lia<PERSON>('@frontend', dirname(dirname(__DIR__)) . '/frontend');
Yii::setAlias('@backend', dirname(dirname(__DIR__)) . '/backend');
Yii::set<PERSON>lia<PERSON>('@console', dirname(dirname(__DIR__)) . '/console');

Yii::setAlias('@gmuAssets', '@frontend/web/gmu-assets');
Yii::setAlias('@examCoverImageUpload', '/var/www/html/getmyuni/assets/images/main/exam');
// Yii::setAlias('@examEditorImage', '@gmuAssets/images/exam_description');
Yii::set<PERSON>lia<PERSON>('@upload', '/var/www/html/getmyuni/assets/images/main/exam');
// Yii::setAlias('@getmyuniExamAsset', 'https://www.getmyuni.com/assets/images/main/exam');
Yii::setAlias('@getmyuniExamAsset', DataHelper::s3Path(null, 'exam_genral', 'path'));

Yii::setAlias('@examTinymceUpload', '/var/www/html/getmyuni/assets/images/main/exam/content');
// Yii::setAlias('@examTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/exam/content');
Yii::setAlias('@examTinymceFrontend', DataHelper::s3Path(null, 'exam', 'path'));

Yii::setAlias('@collegeTinymceUpload', '/var/www/html/getmyuni/assets/images/main/college/content');
// Yii::setAlias('@collegeTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/college/content');
Yii::setAlias('@collegeTinymceFrontend', DataHelper::s3Path(null, 'college', 'path'));

Yii::setAlias('@articleGeneralUpload', '/var/www/html/getmyuni/assets/images/articles');
Yii::setAlias('@ncertGeneralUpload', '/var/www/html/getmyuni/assets/images/ncert');
// Yii::setAlias('@articleGeneralFrontend', 'https://www.getmyuni.com/assets/images/articles');
Yii::setAlias('@articleGeneralFrontend', DataHelper::s3Path(null, 'article_general', 'path'));
Yii::setAlias('@ncertGeneralFrontend', DataHelper::s3Path(null, 'ncert_general', 'path'));

Yii::setAlias('@studyAbroadArticleTinymce', 'https://getmyuni.azureedge.net/assets/images/study-abroad/tinymce/articles');
// Yii::setAlias('@gmuAzureStudyAbroadUpload', 'https://getmyuni.azureedge.net/assets/images/study-abroad/articles');
Yii::setAlias('@gmuAzureStudyAbroadUpload', DataHelper::s3Path(null, 'article_study_abroad', 'path'));
Yii::setAlias('@articleTinymceUpload', '@articleGeneralUpload/content');
Yii::setAlias('@articleTinymceFrontend', '@articleGeneralFrontend/content');

Yii::setAlias('@ncertTinymceUpload', '@ncertGeneralUpload/content');
Yii::setAlias('@ncertTinymceFrontend', '@ncertGeneralFrontend/content');

Yii::setAlias('@categoryTinymceUpload', '/var/www/html/getmyuni/assets/category/content');
// Yii::setAlias('@categoryTinymceFrontend', 'https://www.getmyuni.com/assets/category/content');
Yii::setAlias('@categoryTinymceFrontend', DataHelper::s3Path(null, 'category', 'path'));

Yii::setAlias('@profileDPUpload', '/var/www/html/getmyuni/assets/images/author');
// Yii::setAlias('@profileDPFrontend', 'https://www.getmyuni.com/assets/images/author');
Yii::setAlias('@profileDPFrontend', DataHelper::s3Path(null, 'user', 'path'));

Yii::setAlias('@gmuAzerUpload', 'https://getmyuni.azureedge.net/assets/images');

Yii::setAlias('@gmuAzerUploadBackend', DataHelper::s3Path(null, 'board_schema', 'path'));

Yii::setAlias('@boardLogoUploadPath', '/var/www/html/getmyuni/assets/images/board-logos/');
// Yii::setAlias('@boardLogoFrontend', 'https://www.getmyuni.com/assets/images/board-logos');
Yii::setAlias('@boardLogoFrontend', DataHelper::s3Path(null, 'board_genral', 'path'));
Yii::setAlias('@boardTinymceUpload', '/var/www/html/getmyuni/assets/images/main/board/content');
// Yii::setAlias('@boardTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/board/content');
Yii::setAlias('@boardTinymceFrontend', DataHelper::s3Path(null, 'board', 'path'));
Yii::setAlias('@samplePaperCoverImageUpload', '/var/www/html/getmyuni/assets/images/main/board/paper');
// Yii::setAlias('@samplePaperCoverImageFrontend', 'https://www.getmyuni.com/assets/images/main/board/paper');
Yii::setAlias('@samplePaperCoverImageFrontend', DataHelper::s3Path(null, 'board_sample_papers', 'path'));

Yii::setAlias('@newsSitemapPath', '/var/www/html/getmyuni/assets/sitemap');
Yii::setAlias('@gmuAzerUploadSourceRanking', '@gmuAzerUpload/ranking_source_new');

// Yii::setAlias('@gmuAzureCollegeImage', 'https://getmyuni.azureedge.net/college-image/');
Yii::setAlias('@gmuAzureCollegeImage', DataHelper::s3Path(null, 'college_genral', 'path'));
Yii::setAlias('@gmuAzureCollegeImageLogo', DataHelper::s3Path(null, 'college_logo', 'path'));

Yii::setAlias('@gmuAzureClpCollegeLogo', DataHelper::s3Path(null, 'clp_college_logo', 'path'));
Yii::setAlias('@gmuAzureClpCollegeBanner', DataHelper::s3Path(null, 'clp_college_banner', 'path'));

Yii::setAlias('@gmuAzureSaCollegeImage', DataHelper::s3Path(null, 'sa_college_genral', 'path'));
Yii::setAlias('@gmuAzureSaCollegeImageLogo', DataHelper::s3Path(null, 'sa_college_logo', 'path'));

Yii::setAlias('@dynamicCtaImageUploadPath', '/var/www/html/getmyuni/assets/dynamiccta/images');
Yii::setAlias('@dynamicCtaFileUploadPath', '/var/www/html/getmyuni/assets/dynamiccta/pdfs');
Yii::setAlias('@dynamicCtaImageFrontend', 'https://www.getmyuni.com/assets/dynamiccta/images');
Yii::setAlias('@dynamicCtaImageFrontend', DataHelper::s3Path(null, 'dynamic_cta_image', 'path'));
// Yii::setAlias('@dynamicCtaFileFrontend', 'https://www.getmyuni.com/assets/dynamiccta/pdfs');
Yii::setAlias('@dynamicCtaFileFrontend', DataHelper::s3Path(null, 'dynamic_cta_file', 'path'));

Yii::setAlias('@courseTinymceUpload', '/var/www/html/getmyuni/assets/images/main/course/content');
// Yii::setAlias('@courseTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/course/content');
Yii::setAlias('@courseTinymceFrontend', DataHelper::s3Path(null, 'course', 'path'));


Yii::setAlias('@cityImageUploadPath', '/var/www/html/getmyuni/assets/images/city-logos/');
// Yii::setAlias('@cityImageFrontend', 'https://www.getmyuni.com/assets/images/city-logos');
Yii::setAlias('@cityImageFrontend', DataHelper::s3Path(null, 'city', 'path'));


Yii::setAlias('@newsImageUploadPath', '/var/www/html/getmyuni/assets/images/news-images/');
// Yii::setAlias('@newsImageFrontend', 'https://www.getmyuni.com/assets/images/news-images');
Yii::setAlias('@newsImageFrontend', DataHelper::s3Path(null, 'news_genral', 'path'));

Yii::setAlias('@newsTinymceUpload', '/var/www/html/getmyuni/assets/images/main/news/content');
// Yii::setAlias('@newsTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/news/content');
Yii::setAlias('@newsTinymceFrontend', DataHelper::s3Path(null, 'news', 'path'));

Yii::setAlias('@boardSamplePaperTinymceUpload', '/var/www/html/getmyuni/assets/images/main/board-sample-paper/content');
// Yii::setAlias('@newsTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/news/content');
Yii::setAlias('@boardSamplePaperTinymceFrontend', DataHelper::s3Path(null, 'board-sample-paper', 'path'));
// Yii::setAlias('@newsTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/news/content');

Yii::setAlias('@liveNewsTinymceUpload', '/var/www/html/getmyuni/assets/images/main/live-news/content');
Yii::setAlias('@liveNewsTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/live-news/content');
Yii::setAlias('@collegeRankPublisherFrontend', DataHelper::s3Path(null, 'ranking', 'path'));


Yii::setAlias('@careerGeneralFrontend', DataHelper::s3Path(null, Career::ENTITY_CAREER, 'path'));
Yii::setAlias('@careerTinymceUpload', '/var/www/html/getmyuni/assets/images/main/career/content');
// Yii::setAlias('@courseTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/course/content');
Yii::setAlias('@careerTinymceFrontend', DataHelper::s3Path(null, CareerContent::CAREER_CONTENT_ENTITY, 'path'));

Yii::setAlias('@homePageSlider', DataHelper::s3Path(null, 'homepage_slides', 'path'));

Yii::setAlias('@scholarshipGeneralFrontend', DataHelper::s3Path(null, Scholarship::ENTITY_SCHOLARSHIP_URL, 'path'));
Yii::setAlias('@scholarshipGeneralFrontendLogo', DataHelper::s3Path(null, Scholarship::ENTITY_SCHOLARSHIP_LOGO, 'path'));
Yii::setAlias('@scholarshipTinymceUpload', '/var/www/html/getmyuni/assets/images/main/scholarship/content');
// Yii::setAlias('@scholarshipTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/course/content');
Yii::setAlias('@scholarshipTinymceFrontend', DataHelper::s3Path(null, ScholarshipContent::SCHOLARSHIP_CONTENT_ENTITY, 'path'));
Yii::setAlias('@scholarshipcategoryTinymceFrontend', DataHelper::s3Path(null, ScholarshipCategory::SCHOLARSHIP_CATEGORY_CONTENT, 'path'));

Yii::setAlias('@olympiadGeneralFrontend', DataHelper::s3Path(null, Olympiad::ENTITY_OLYMPIAD, 'path'));
Yii::setAlias('@olympiadTinymceUpload', '/var/www/html/getmyuni/assets/images/main/olympiad/content');
// Yii::setAlias('@courseTinymceFrontend', 'https://www.getmyuni.com/assets/images/main/course/content');
Yii::setAlias('@olympiadTinymceFrontend', DataHelper::s3Path(null, OlympiadContent::ENTITY_OLYMPIAD_CONTENT, 'path'));

Yii::setAlias('@pdfAssets', '@frontend/web/yas/pdf');
