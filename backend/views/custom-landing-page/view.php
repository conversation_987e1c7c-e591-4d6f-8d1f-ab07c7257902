<?php

use common\helpers\DataHelper;
use common\models\City;
use common\models\ClpCollegeImagePanel;
use common\models\ClpCollegeUsp;
use common\models\College;
use common\models\Course;
use common\models\CustomLandingPage;
use common\models\Degree;
use common\models\Program;
use common\models\State;
use common\models\Stream;
use common\models\TopRecruiters;
use frontend\helpers\Url as FrontendUrl;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CustomLandingPage */

$this->title = $model->slug;
$this->params['breadcrumbs'][] = ['label' => 'Custom Landing Pages', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

// Frontend URL for preview
$frontendUrl = FrontendUrl::toGetmyuni() . 'college-admissions/' . $model->slug;
$templates = [
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_ONE,
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO,
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_THREE
];

?>
<style>
    .badge {
        background-color: #eeeeee !important;
        color: black;
        font-weight: 400;
        border: 1px solid #ccc;
        font-size: 13px;
    }
</style>
<div class="custom-landing-page-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (in_array($model->template_id, $templates)): ?>
            <?= Html::a('Top Recruiters', ['top-recruiters/tabular-form', 'college_id' => $model->college_id, 'clp_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
            <?= Html::a('College USP', ['clp-college-usp/tabular-form', 'college_id' => $model->college_id, 'clp_id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Script Injection', ['script-injection', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('<i class="fa fa-eye"></i> Preview', $frontendUrl, ['class' => 'btn btn-info btn-flat', 'target' => '_blank']) ?>
    </div>
    <div class="box-body">
        <div class="row">
            <!-- Left Column -->
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <td width="40%"><strong>Template:</strong></td>
                        <td><?= ArrayHelper::getValue(DataHelper::getConstantList('TEMPLATE', CustomLandingPage::class), $model->template_id) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Page Title:</strong></td>
                        <td><?= Html::encode($model->page_title) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Page Sub Title:</strong></td>
                        <td><?= Html::encode($model->page_sub_title) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Page Description:</strong></td>
                        <td><?= Html::encode($model->page_description) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Form Title:</strong></td>
                        <td><?= Html::encode($model->form_title) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Form Description:</strong></td>
                        <td><?= Html::encode($model->form_description) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Form CTA Text:</strong></td>
                        <td><?= Html::encode($model->form_cta_text) ?></td>
                    </tr>
                    <tr>
                        <td><strong>Form City:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->city_id) ? $model->city_id : json_decode($model->city_id, true);
                            $names = City::find()->select('name')->where(['id' => $ids])->column();
                            if (!empty($names)) {
                                foreach ($names as $name) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($name) . '</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Form State:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->state_id) ? $model->state_id : json_decode($model->state_id, true);
                            $names = State::find()->select('name')->where(['id' => $ids])->column();
                            if (!empty($names)) {
                                foreach ($names as $name) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($name) . '</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>College:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->college_id) ? $model->college_id : json_decode($model->college_id, true);
                            $collegeData = College::find()->where(['id' => $ids])->all();
                            if (!empty($collegeData)) {
                                echo '<div style="max-height: 100px; overflow-y: auto;">';
                                foreach ($collegeData as $college) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($college->name) . '</span>';
                                }
                                echo '</div>';
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Right Column -->
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <td width="40%"><strong>Form Stream:</strong></td>
                        <td>
                            <?php
                            $streamIds = is_array($model->stream_id) ? $model->stream_id : json_decode($model->stream_id, true);
                            if (is_array($streamIds) && !empty($streamIds)) {
                                $streams = Stream::find()->where(['id' => $streamIds])->all();
                                foreach ($streams as $stream) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($stream->name) . '</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Form Level:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->level_id) ? $model->level_id : json_decode($model->level_id, true);
                            if (is_array($ids) && !empty($ids)) {
                                $levels = Degree::find()->where(['id' => $ids])->all();
                                foreach ($levels as $level) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($level->name) . '</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Form Course:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->course_id) ? $model->course_id : json_decode($model->course_id, true);
                            if (is_array($ids) && !empty($ids)) {
                                $courses = Course::find()->where(['id' => $ids])->all();
                                echo '<div style="max-height: 80px; overflow-y: auto;">';
                                foreach ($courses as $course) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($course->name) . '</span>';
                                }
                                echo '</div>';
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Form Program:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->program_id) ? $model->program_id : json_decode($model->program_id, true);
                            if (is_array($ids) && !empty($ids)) {
                                $programs = Program::find()->where(['id' => $ids])->all();
                                echo '<div style="max-height: 80px; overflow-y: auto;">';
                                foreach ($programs as $program) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($program->name) . '</span>';
                                }
                                echo '</div>';
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Form Campus:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->campus) ? $model->campus : json_decode($model->campus, true);
                            $collegeData = College::find()->where(['id' => $ids])->all();
                            if (!empty($collegeData)) {
                                echo '<div style="max-height: 100px; overflow-y: auto;">';
                                foreach ($collegeData as $college) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($college->name) . '</span>';
                                }
                                echo '</div>';
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Stream Widget:</strong></td>
                        <td>
                            <?php
                            $streamIds = $model->stream_widget;
                            if (is_string($streamIds)) {
                                $streamIds = json_decode($streamIds, true);
                            }
                            if (is_array($streamIds) && !empty($streamIds)) {
                                $streams = Stream::find()->where(['id' => $streamIds])->all();
                                foreach ($streams as $stream) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($stream->name) . '</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Program Widget:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->program_widget) ? $model->program_widget : json_decode($model->program_widget, true);
                            if (is_array($ids) && !empty($ids)) {
                                $programs = Program::find()->where(['id' => $ids])->all();
                                echo '<div style="max-height: 60px; overflow-y: auto;">';
                                foreach ($programs as $program) {
                                    echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($program->name) . '</span>';
                                }
                                echo '</div>';
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>

                    <tr>
                        <td><strong>Partner College Widget:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->partner_college_widget) ? $model->partner_college_widget : json_decode($model->partner_college_widget, true);
                            if (is_array($ids) && !empty($ids)) {
                                $collegeData = College::find()->where(['id' => $ids])->all();
                                if (!empty($collegeData)) {
                                    echo '<div style="max-height: 100px; overflow-y: auto;">';
                                    foreach ($collegeData as $college) {
                                        echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($college->name) . '</span>';
                                    }
                                    echo '</div>';
                                } else {
                                    echo '<span class="text-muted">Not specified</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Campus Widget:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->campus_widget) ? $model->campus_widget : json_decode($model->campus_widget, true);
                            if (is_array($ids) && !empty($ids)) {
                                $collegeData = College::find()->where(['id' => $ids])->all();
                                if (!empty($collegeData)) {
                                    echo '<div style="max-height: 100px; overflow-y: auto;">';
                                    foreach ($collegeData as $college) {
                                        echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($college->name) . '</span>';
                                    }
                                    echo '</div>';
                                } else {
                                    echo '<span class="text-muted">Not specified</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Recruiters Widget:</strong></td>
                        <td>
                            <?php
                            $ids = is_array($model->recruitment_widget) ? $model->recruitment_widget : json_decode($model->recruitment_widget, true);
                            if (is_array($ids) && !empty($ids)) {
                                $recruits = TopRecruiters::find()->where(['id' => $ids])->all();
                                if (!empty($recruits)) {
                                    echo '<div style="max-height: 100px; overflow-y: auto;">';
                                    foreach ($recruits as $recruit) {
                                        echo '<span class="badge badge-info" style="margin-right: 5px; margin-bottom: 3px; background-color: #5bc0de;">' . Html::encode($recruit->recruiter_name) . '</span>';
                                    }
                                    echo '</div>';
                                } else {
                                    echo '<span class="text-muted">Not specified</span>';
                                }
                            } else {
                                echo '<span class="text-muted">Not specified</span>';
                            }
                            ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Is GMU Logo:</strong></td>
                        <td>
                            <span class="label label-<?= $model->is_gmu_logo ? 'success' : 'default' ?>">
                                <?= ArrayHelper::getValue(DataHelper::getConstantList('IS_GMU_LOGO', CustomLandingPage::class), $model->is_gmu_logo) ?>
                            </span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td>
                            <span class="label label-<?= $model->status == CustomLandingPage::STATUS_ACTIVE ? 'success' : 'default' ?>">
                                <?= ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CustomLandingPage::class), $model->status) ?>
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- Full Width Sections -->
        <div class="row" style="margin-top: 20px;">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label">College USPs Widget</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->college_usp_widget) ? $model->college_usp_widget : json_decode($model->college_usp_widget, true);
                        if (is_array($ids) && !empty($ids)) {
                            $collegeUsps = \common\models\ClpCollegeUsp::find()->where(['id' => $ids])->all();
                            if (!empty($collegeUsps)) {
                                echo '<table class="table table-bordered table-striped"><thead><tr><th>Title</th><th>Subtitle</th></tr></thead><tbody>';
                                foreach ($collegeUsps as $collegeUsp) {
                                    echo '<tr>';
                                    echo '<td>' . Html::encode($collegeUsp->usp_title ?? '-') . '</td>';
                                    echo '<td>' . Html::encode($collegeUsp->usp_sub_title ?? '-') . '</td>';
                                    echo '</tr>';
                                }
                                echo '</tbody></table>';
                            } else {
                                echo '<i>No college USPs found</i>';
                            }
                        } else {
                            echo '<i>No college USPs configured</i>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label">Featured Content</label>
                    <div class="form-control-static">
                        <?php
                        if (is_string($model->featured_content)) {
                            $content = json_decode($model->featured_content, true);
                        } else {
                            $content = $model->featured_content;
                        }

                        if (empty($content)) {
                            echo '<i>No featured content</i>';
                        } else {
                            echo '<table class="table table-bordered table-striped"><thead><tr><th>Title</th><th>Subtitle</th><th>CTA</th></tr></thead><tbody>';
                            foreach ($content as $item) {
                                echo '<tr>';
                                echo '<td>' . Html::encode($item['fc_title'] ?? '-') . '</td>';
                                echo '<td>' . Html::encode($item['fc_subtitle'] ?? '-') . '</td>';
                                echo '<td>' . Html::encode($item['fc_cta'] ?? '-') . '</td>';
                                echo '</tr>';
                            }
                            echo '</tbody></table>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label">Script Injection</label>
                    <div class="form-control-static">
                        <?php
                        if (is_string($model->script_injection)) {
                            $data = json_decode($model->script_injection, true);
                        } else {
                            $data = $model->script_injection;
                        }

                        if (empty($data) || !is_array($data)) {
                            echo '<i>No script injection data</i>';
                        } else {
                            echo '<table class="table table-bordered table-striped"><thead><tr><th>Key</th><th>Value</th></tr></thead><tbody>';
                            foreach ($data as $key => $value) {
                                // If value is an array, convert it to string
                                $valueFormatted = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
                                echo '<tr>';
                                echo '<td>' . Html::encode($key) . '</td>';
                                echo '<td>' . Html::encode($valueFormatted) . '</td>';
                                echo '</tr>';
                            }
                            echo '</tbody></table>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (in_array($model->template_id, $templates) && !empty($collegeImagePanel)): ?>
    <div class="clp-college-image-panel-view box box-primary" style="margin-top: 20px;">
        <div class="box-header">
            <h3 class="box-title"><b>College Image Panel</b></h3>
            <?= Html::a('Update', ['clp-college-image-panel/create', 'college_id' => $model->college_id, 'clp_id' => $model->id], ['class' => 'btn btn-primary btn-flat pull-right']) ?>
        </div>
        <div class="box-body">
            <div class="row">
                <!-- Images Section -->
                <div class="col-md-4">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Logo Image</label>
                                <div class="image-preview">
                                    <?php if (!empty($collegeImagePanel->logo_image)): ?>
                                        <?= Html::img(\Yii::$aliases['@gmuAzureClpCollegeLogo'] . '/' . $collegeImagePanel->logo_image, ['width' => '120', 'height' => '120', 'class' => 'img-thumbnail']) ?>
                                    <?php else: ?>
                                        <div class="no-image-placeholder" style="width: 120px; height: 120px; border: 2px dashed #ddd; display: flex; align-items: center; justify-content: center; color: #999;">
                                            <span>No Logo Image</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="control-label">Banner Image</label>
                                <div class="image-preview">
                                    <?php if (!empty($collegeImagePanel->banner_image)): ?>
                                        <?= Html::img(\Yii::$aliases['@gmuAzureClpCollegeBanner'] . '/' . $collegeImagePanel->banner_image, ['width' => '120', 'height' => '120', 'class' => 'img-thumbnail']) ?>
                                    <?php else: ?>
                                        <div class="no-image-placeholder" style="width: 120px; height: 120px; border: 2px dashed #ddd; display: flex; align-items: center; justify-content: center; color: #999;">
                                            <span>No Banner Image</span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Details Section -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="control-label">Status</label>
                        <div class="form-control-static">
                            <?php if (isset($collegeImagePanel->status)): ?>
                                <span class="label label-<?= $collegeImagePanel->status == ClpCollegeImagePanel::STATUS_ACTIVE ? 'success' : 'default' ?>">
                                    <?= ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class), $collegeImagePanel->status) ?>
                                </span>
                            <?php else: ?>
                                <span class="text-muted">Not specified</span>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label">Created At</label>
                        <div class="form-control-static">
                            <?= Yii::$app->formatter->asDatetime($collegeImagePanel->created_at) ?>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="control-label">Updated At</label>
                        <div class="form-control-static">
                            <?= Yii::$app->formatter->asDatetime($collegeImagePanel->updated_at) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>