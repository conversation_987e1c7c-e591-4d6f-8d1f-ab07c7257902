<?php

use common\helpers\DataHelper;
use common\models\City;
use common\models\ClpCollegeImagePanel;
use common\models\College;
use common\models\Course;
use common\models\CustomLandingPage;
use common\models\Degree;
use common\models\Program;
use common\models\State;
use common\models\Stream;
use frontend\helpers\Url as FrontendUrl;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CustomLandingPage */

$this->title = $model->slug;
$this->params['breadcrumbs'][] = ['label' => 'Custom Landing Pages', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

// Frontend URL for preview
$frontendUrl = FrontendUrl::toGetmyuni() . 'college-admissions/' . $model->slug;
$templates = [
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_ONE,
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO,
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_THREE
];

?>
<div class="custom-landing-page-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (in_array($model->template_id, $templates)): ?>
            <?= Html::a('Top Recruiters', ['top-recruiters/tabular-form', 'college_id' => $model->college_id], ['class' => 'btn btn-primary btn-flat']) ?>
            <?= Html::a('College USP', ['clp-college-usp/tabular-form', 'college_id' => $model->college_id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Script Injection', ['script-injection', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('<i class="fa fa-eye"></i> Preview', $frontendUrl, ['class' => 'btn btn-info btn-flat', 'target' => '_blank']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $model,
            'attributes' => [
                [
                    'label' => 'Template',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('TEMPLATE', CustomLandingPage::class), $model->template_id)
                ],
                'page_title',
                'page_sub_title',
                'page_description',
                'form_title',
                'form_description',
                'form_cta_text',
                [
                    'label' => 'City',
                    'value' => function ($model) {
                        $ids = is_array($model->city_id) ? $model->city_id : json_decode($model->city_id, true);
                        $names = City::find()->select('name')->where(['id' => $ids])->column();
                        return implode(', ', $names);
                    },
                ],
                [
                    'label' => 'State',
                    'value' => function ($model) {
                        $ids = is_array($model->state_id) ? $model->state_id : json_decode($model->state_id, true);
                        $names = State::find()->select('name')->where(['id' => $ids])->column();
                        return implode(', ', $names);
                    },
                ],
                [
                    'label' => 'College',
                    'value' => function ($model) {
                        $ids = is_array($model->college_id) ? $model->college_id : json_decode($model->college_id, true);
                        $collegeData = College::find()->where(['id' => $ids])->all();
                        $mapped = ArrayHelper::map($collegeData, 'id', 'name');
                        return implode(', ', $mapped);
                    },
                ],
                [
                    'label' => 'Stream',
                    'value' => function ($model) {
                        $streamIds = is_array($model->stream_id) ? $model->stream_id : json_decode($model->stream_id, true);
                        if (!is_array($streamIds)) {
                            return '';
                        }
                        $streams = Stream::find()->where(['id' => $streamIds])->all();
                        $mapped = ArrayHelper::map($streams, 'id', 'name');
                        return implode(', ', $mapped);
                    },
                ],
                [
                    'label' => 'Level',
                    'value' => function ($model) {
                        $ids = is_array($model->level_id) ? $model->level_id : json_decode($model->level_id, true);
                        $levels = Degree::find()->where(['id' => $ids])->all();
                        $mapped = \yii\helpers\ArrayHelper::map($levels, 'id', 'name');
                        return implode(', ', $mapped);
                    },
                ],
                [
                    'label' => 'Course',
                    'value' => function ($model) {
                        $ids = is_array($model->course_id) ? $model->course_id : json_decode($model->course_id, true);
                        $courses = Course::find()->where(['id' => $ids])->all();
                        $mapped = \yii\helpers\ArrayHelper::map($courses, 'id', 'name');
                        return implode(', ', $mapped);
                    },
                ],
                [
                    'label' => 'Program',
                    'value' => function ($model) {
                        $ids = is_array($model->program_id) ? $model->program_id : json_decode($model->program_id, true);
                        $programs = Program::find()->where(['id' => $ids])->all();
                        $mapped = \yii\helpers\ArrayHelper::map($programs, 'id', 'name');
                        return implode(', ', $mapped);
                    },
                ],
                [
                    'label' => 'Campus',
                    'value' => function ($model) {
                        return empty($model->campus) ? '' : json_encode($model->campus, true);
                    },
                ],
                [
                    'label' => 'Featured Content',
                    'format' => 'raw',
                    'value' => function ($model) {
                        if (is_string($model->featured_content)) {
                            $content = json_decode($model->featured_content, true);
                        } else {
                            $content = $model->featured_content;
                        }

                        if (empty($content)) {
                            return '<i>No featured content</i>';
                        }

                        $html = '<table class="table table-bordered"><thead><tr><th>Title</th><th>Subtitle</th><th>CTA</th></tr></thead><tbody>';
                        foreach ($content as $item) {
                            $html .= '<tr>';
                            $html .= '<td>' . Html::encode($item['fc_title'] ?? '-') . '</td>';
                            $html .= '<td>' . Html::encode($item['fc_subtitle'] ?? '-') . '</td>';
                            $html .= '<td>' . Html::encode($item['fc_cta'] ?? '-') . '</td>';
                            $html .= '</tr>';
                        }
                        $html .= '</tbody></table>';

                        return $html;
                    },
                ],
                [
                    'label' => 'Stream Widget',
                    'value' => function ($model) {
                        $streamIds = $model->stream_widget;
                        if (is_string($streamIds)) {
                            $streamIds = json_decode($streamIds, true);
                        }
                        if (!is_array($streamIds)) {
                            $streamIds = [];
                        }
                        $streams = Stream::find()->where(['id' => $streamIds])->all();
                        $mapped = ArrayHelper::map($streams, 'id', 'name');
                        return implode(', ', $mapped);
                    },
                ],
                // 'college_usp_widget',
                // 'recruitment_widget',
                [
                    'label' => 'Program',
                    'value' => function ($model) {
                        $ids = is_array($model->program_widget) ? $model->program_widget : json_decode($model->program_widget, true);
                        $programs = Program::find()->where(['id' => $ids])->all();
                        $mapped = \yii\helpers\ArrayHelper::map($programs, 'id', 'name');
                        return implode(', ', $mapped);
                    },
                ],
                // 'campus_widget',
                [
                    'label' => 'Script Injection',
                    'format' => 'raw',
                    'value' => function ($model) {
                        if (is_string($model->script_injection)) {
                            $data = json_decode($model->script_injection, true);
                        } else {
                            $data = $model->script_injection;
                        }

                        if (empty($data) || !is_array($data)) {
                            return '<i>No script injection data</i>';
                        }

                        $html = '<table class="table table-bordered"><thead><tr><th>Key</th><th>Value</th></tr></thead><tbody>';
                        foreach ($data as $key => $value) {
                            // If value is an array, convert it to string
                            $valueFormatted = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
                            $html .= '<tr>';
                            $html .= '<td>' . Html::encode($key) . '</td>';
                            $html .= '<td>' . Html::encode($valueFormatted) . '</td>';
                            $html .= '</tr>';
                        }
                        $html .= '</tbody></table>';

                        return $html;
                    },
                ],
                [
                    'attribute' => 'is_gmu_logo',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('IS_GMU_LOGO', CustomLandingPage::class), $model->status)
                ],
                [
                    'label' => 'Status',
                    'attribute' => 'status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CustomLandingPage::class), $model->status)
                ],
            ],
        ]) ?>
    </div>
</div>

<?php if (in_array($model->template_id, $templates) && !empty($collegeImagePanel)): ?>
<div class="clp-college-image-panel-view box box-primary" style="margin-top: 20px;">
    <div class="box-header">
        <h3 class="box-title">College Image Panel</h3>
        <?= Html::a('Update', ['clp-college-image-panel/create', 'college_id' => $collegeImagePanel->college_id, 'clp_id' => $model->id], ['class' => 'btn btn-primary btn-flat pull-right']) ?>
    </div>
    <div class="box-body table-responsive no-padding">
        <?= DetailView::widget([
            'model' => $collegeImagePanel,
            'attributes' => [
                [
                    'attribute' => 'logo_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->logo_image)) {
                            return Html::img(\Yii::$aliases['@gmuAzureClpCollegeLogo'] . '/' . $model->logo_image, ['width' => '70', 'height' => '70']);
                        } else {
                            return '<span class="not-set">Logo Image is missing.</span>';
                        }
                    }
                ],
                [
                    'attribute' => 'banner_image',
                    'format' => 'html',
                    'value' => function ($model) {
                        if (!empty($model->banner_image)) {
                            return Html::img(\Yii::$aliases['@gmuAzureClpCollegeBanner'] . '/' . $model->banner_image, ['width' => '70', 'height' => '70']);
                        } else {
                            return '<span class="not-set">Banner Image is missing.</span>';
                        }
                    }
                ],
                [
                    'attribute' => 'status',
                    'label' => 'Status',
                    'value' => ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class), $collegeImagePanel->status)
                ],
                'created_at:datetime',
                'updated_at:datetime',
            ],
        ]) ?>
    </div>
</div>
<?php endif; ?>