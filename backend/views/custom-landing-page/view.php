<?php

use common\helpers\DataHelper;
use common\models\City;
use common\models\ClpCollegeImagePanel;
use common\models\College;
use common\models\Course;
use common\models\CustomLandingPage;
use common\models\Degree;
use common\models\Program;
use common\models\State;
use common\models\Stream;
use frontend\helpers\Url as FrontendUrl;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\widgets\DetailView;

/* @var $this yii\web\View */
/* @var $model common\models\CustomLandingPage */

$this->title = $model->slug;
$this->params['breadcrumbs'][] = ['label' => 'Custom Landing Pages', 'url' => ['index']];
$this->params['breadcrumbs'][] = $this->title;

// Frontend URL for preview
$frontendUrl = FrontendUrl::toGetmyuni() . 'college-admissions/' . $model->slug;
$templates = [
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_ONE,
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_TWO,
    CustomLandingPage::TEMPLATE_COLLEGE_BASED_THREE
];

?>
<div class="custom-landing-page-view box box-primary">
    <div class="box-header">
        <?= Html::a('Update', ['update', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php if (in_array($model->template_id, $templates)): ?>
            <?= Html::a('Top Recruiters', ['top-recruiters/tabular-form', 'college_id' => $model->college_id], ['class' => 'btn btn-primary btn-flat']) ?>
            <?= Html::a('College USP', ['clp-college-usp/tabular-form', 'college_id' => $model->college_id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?php endif; ?>
        <?= Html::a('Script Injection', ['script-injection', 'id' => $model->id], ['class' => 'btn btn-primary btn-flat']) ?>
        <?= Html::a('<i class="fa fa-eye"></i> Preview', $frontendUrl, ['class' => 'btn btn-info btn-flat', 'target' => '_blank']) ?>
    </div>
    <div class="box-body">
        <div class="row">
            <!-- Left Column -->
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Template</label>
                    <div class="form-control-static">
                        <?= ArrayHelper::getValue(DataHelper::getConstantList('TEMPLATE', CustomLandingPage::class), $model->template_id) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Page Title</label>
                    <div class="form-control-static">
                        <?= Html::encode($model->page_title) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Page Sub Title</label>
                    <div class="form-control-static">
                        <?= Html::encode($model->page_sub_title) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Page Description</label>
                    <div class="form-control-static">
                        <?= Html::encode($model->page_description) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Form Title</label>
                    <div class="form-control-static">
                        <?= Html::encode($model->form_title) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Form Description</label>
                    <div class="form-control-static">
                        <?= Html::encode($model->form_description) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Form CTA Text</label>
                    <div class="form-control-static">
                        <?= Html::encode($model->form_cta_text) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">City</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->city_id) ? $model->city_id : json_decode($model->city_id, true);
                        $names = City::find()->select('name')->where(['id' => $ids])->column();
                        echo implode(', ', $names);
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">State</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->state_id) ? $model->state_id : json_decode($model->state_id, true);
                        $names = State::find()->select('name')->where(['id' => $ids])->column();
                        echo implode(', ', $names);
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">College</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->college_id) ? $model->college_id : json_decode($model->college_id, true);
                        $collegeData = College::find()->where(['id' => $ids])->all();
                        $mapped = ArrayHelper::map($collegeData, 'id', 'name');
                        echo implode(', ', $mapped);
                        ?>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="col-md-6">
                <div class="form-group">
                    <label class="control-label">Stream</label>
                    <div class="form-control-static">
                        <?php
                        $streamIds = is_array($model->stream_id) ? $model->stream_id : json_decode($model->stream_id, true);
                        if (is_array($streamIds)) {
                            $streams = Stream::find()->where(['id' => $streamIds])->all();
                            $mapped = ArrayHelper::map($streams, 'id', 'name');
                            echo implode(', ', $mapped);
                        }
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Level</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->level_id) ? $model->level_id : json_decode($model->level_id, true);
                        $levels = Degree::find()->where(['id' => $ids])->all();
                        $mapped = ArrayHelper::map($levels, 'id', 'name');
                        echo implode(', ', $mapped);
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Course</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->course_id) ? $model->course_id : json_decode($model->course_id, true);
                        $courses = Course::find()->where(['id' => $ids])->all();
                        $mapped = ArrayHelper::map($courses, 'id', 'name');
                        echo implode(', ', $mapped);
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Program</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->program_id) ? $model->program_id : json_decode($model->program_id, true);
                        $programs = Program::find()->where(['id' => $ids])->all();
                        $mapped = ArrayHelper::map($programs, 'id', 'name');
                        echo implode(', ', $mapped);
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Campus</label>
                    <div class="form-control-static">
                        <?= empty($model->campus) ? '' : json_encode($model->campus, true) ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Stream Widget</label>
                    <div class="form-control-static">
                        <?php
                        $streamIds = $model->stream_widget;
                        if (is_string($streamIds)) {
                            $streamIds = json_decode($streamIds, true);
                        }
                        if (!is_array($streamIds)) {
                            $streamIds = [];
                        }
                        $streams = Stream::find()->where(['id' => $streamIds])->all();
                        $mapped = ArrayHelper::map($streams, 'id', 'name');
                        echo implode(', ', $mapped);
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Program Widget</label>
                    <div class="form-control-static">
                        <?php
                        $ids = is_array($model->program_widget) ? $model->program_widget : json_decode($model->program_widget, true);
                        $programs = Program::find()->where(['id' => $ids])->all();
                        $mapped = ArrayHelper::map($programs, 'id', 'name');
                        echo implode(', ', $mapped);
                        ?>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Is GMU Logo</label>
                    <div class="form-control-static">
                        <span class="label label-<?= $model->is_gmu_logo ? 'success' : 'default' ?>">
                            <?= ArrayHelper::getValue(DataHelper::getConstantList('IS_GMU_LOGO', CustomLandingPage::class), $model->is_gmu_logo) ?>
                        </span>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label">Status</label>
                    <div class="form-control-static">
                        <span class="label label-<?= $model->status == CustomLandingPage::STATUS_ACTIVE ? 'success' : 'default' ?>">
                            <?= ArrayHelper::getValue(DataHelper::getConstantList('STATUS', CustomLandingPage::class), $model->status) ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Full Width Sections -->
        <div class="row" style="margin-top: 20px;">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label">Featured Content</label>
                    <div class="form-control-static">
                        <?php
                        if (is_string($model->featured_content)) {
                            $content = json_decode($model->featured_content, true);
                        } else {
                            $content = $model->featured_content;
                        }

                        if (empty($content)) {
                            echo '<i>No featured content</i>';
                        } else {
                            echo '<table class="table table-bordered table-striped"><thead><tr><th>Title</th><th>Subtitle</th><th>CTA</th></tr></thead><tbody>';
                            foreach ($content as $item) {
                                echo '<tr>';
                                echo '<td>' . Html::encode($item['fc_title'] ?? '-') . '</td>';
                                echo '<td>' . Html::encode($item['fc_subtitle'] ?? '-') . '</td>';
                                echo '<td>' . Html::encode($item['fc_cta'] ?? '-') . '</td>';
                                echo '</tr>';
                            }
                            echo '</tbody></table>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label class="control-label">Script Injection</label>
                    <div class="form-control-static">
                        <?php
                        if (is_string($model->script_injection)) {
                            $data = json_decode($model->script_injection, true);
                        } else {
                            $data = $model->script_injection;
                        }

                        if (empty($data) || !is_array($data)) {
                            echo '<i>No script injection data</i>';
                        } else {
                            echo '<table class="table table-bordered table-striped"><thead><tr><th>Key</th><th>Value</th></tr></thead><tbody>';
                            foreach ($data as $key => $value) {
                                // If value is an array, convert it to string
                                $valueFormatted = is_array($value) ? json_encode($value, JSON_UNESCAPED_UNICODE) : $value;
                                echo '<tr>';
                                echo '<td>' . Html::encode($key) . '</td>';
                                echo '<td>' . Html::encode($valueFormatted) . '</td>';
                                echo '</tr>';
                            }
                            echo '</tbody></table>';
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if (in_array($model->template_id, $templates) && !empty($collegeImagePanel)): ?>
<div class="clp-college-image-panel-view box box-primary" style="margin-top: 20px;">
    <div class="box-header">
        <h3 class="box-title"><b>College Image Panel</b></h3>
        <?= Html::a('Update', ['clp-college-image-panel/create', 'college_id' => $collegeImagePanel->college_id, 'clp_id' => $model->id], ['class' => 'btn btn-primary btn-flat pull-right']) ?>
    </div>
    <div class="box-body">
        <div class="row">
            <!-- Images Section -->
            <div class="col-md-8">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">Logo Image</label>
                            <div class="image-preview">
                                <?php if (!empty($collegeImagePanel->logo_image)): ?>
                                    <?= Html::img(\Yii::$aliases['@gmuAzureClpCollegeLogo'] . '/' . $collegeImagePanel->logo_image, ['width' => '120', 'height' => '120', 'class' => 'img-thumbnail']) ?>
                                <?php else: ?>
                                    <div class="no-image-placeholder" style="width: 120px; height: 120px; border: 2px dashed #ddd; display: flex; align-items: center; justify-content: center; color: #999;">
                                        <span>No Logo Image</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label">Banner Image</label>
                            <div class="image-preview">
                                <?php if (!empty($collegeImagePanel->banner_image)): ?>
                                    <?= Html::img(\Yii::$aliases['@gmuAzureClpCollegeBanner'] . '/' . $collegeImagePanel->banner_image, ['width' => '120', 'height' => '120', 'class' => 'img-thumbnail']) ?>
                                <?php else: ?>
                                    <div class="no-image-placeholder" style="width: 120px; height: 120px; border: 2px dashed #ddd; display: flex; align-items: center; justify-content: center; color: #999;">
                                        <span>No Banner Image</span>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Details Section -->
            <div class="col-md-4">
                <div class="form-group">
                    <label class="control-label">Status</label>
                    <div class="form-control-static">
                        <span class="label label-<?= $collegeImagePanel->status == ClpCollegeImagePanel::STATUS_ACTIVE ? 'success' : 'default' ?>">
                            <?= ArrayHelper::getValue(DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class), $collegeImagePanel->status) ?>
                        </span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">Created At</label>
                    <div class="form-control-static">
                        <?= Yii::$app->formatter->asDatetime($collegeImagePanel->created_at) ?>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label">Updated At</label>
                    <div class="form-control-static">
                        <?= Yii::$app->formatter->asDatetime($collegeImagePanel->updated_at) ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>