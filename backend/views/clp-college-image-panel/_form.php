<?php

use common\helpers\DataHelper;
use common\models\ClpCollegeImagePanel;
use common\models\College;
use kartik\select2\Select2;
use yii\helpers\ArrayHelper;
use yii\helpers\Html;
use yii\web\JsExpression;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $model common\models\ClpCollegeImagePanel */
/* @var $form yii\widgets\ActiveForm */

$data =  ArrayHelper::map(College::find()->andWhere(['id' => $model->college_id])->all(), 'id', 'name');

?>

<div class="clp-college-image-panel-form box box-primary">
    <?php $form = ActiveForm::begin(); ?>
    <div class="box-body table-responsive">

        <div class="col-md-12">
            <?= $form->field($model, 'college_id')->widget(Select2::classname(), [
                'disabled' => !$model->isNewRecord || $model->college_id,
                'data' => $data, // array of text to show in the tag for the selected items
                'options' => [
                    'placeholder' => '--Select--',
                    'multiple' => false,
                ],
                'pluginOptions' => [
                    'allowClear' => true,
                    'minimumInputLength' => 3,
                    'language' => [
                        'errorLoading' => new JsExpression("function () { return 'Waiting for results...'; }"),
                    ],
                    'ajax' => [
                        'url' => ['../ajax/college-list'],
                        'dataType' => 'json',
                        'data' => new JsExpression('function(params) {return {q:params.term}; }')
                    ],
                    'escapeMarkup' => new JsExpression('function (markup) { return markup; }'),
                    'templateResult' => new JsExpression('function(data) { return data.text; }'),
                    'templateSelection' => new JsExpression('function (data) { return data.text; }'),
                ],
            ])->label('College Name'); ?>
        </div>

        <div class="col-md-6">
            <?php
            if (!empty($model->logo_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureClpCollegeLogo'] . '/' . $model->logo_image, ['width' => '50', 'height' => '50']);
            } ?>
            <?= $form->field($model, 'logo_image')->fileInput() ?>
        </div>
        <div class="col-md-6">
            <?php
            if (!empty($model->banner_image)) {
                echo Html::img(\Yii::$aliases['@gmuAzureClpCollegeBanner'] . '/' . $model->banner_image, ['width' => '50', 'height' => '50']);
            } ?>
            <?= $form->field($model, 'banner_image')->fileInput() ?>
        </div>

        <div class="col-md-12">
            <?= $form->field($model, 'status')->dropDownList(DataHelper::getConstantList('STATUS', ClpCollegeImagePanel::class)); ?>
        </div>
    </div>
    <div class="box-footer">
        <?= Html::submitButton('Save', ['class' => 'btn btn-success btn-flat']) ?>
    </div>
    <?php ActiveForm::end(); ?>
</div>