<?php

namespace backend\controllers;

use common\helpers\DataHelper;
use common\models\ClpCollegeImagePanel;
use common\services\S3Service;
use Yii;
use backend\models\ClpCollegeImagePanelSearch;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ClpCollegeImagePanelController implements the CRUD actions for ClpCollegeImagePanel model.
 */
class ClpCollegeImagePanelController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ClpCollegeImagePanel models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ClpCollegeImagePanelSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ClpCollegeImagePanel model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id, $clp_id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
            'clp_id' => $clp_id
        ]);
    }

    /**
     * Creates a new ClpCollegeImagePanel model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate($college_id, $clp_id)
    {
        $model = ClpCollegeImagePanel::findOne(['college_id' => $college_id]);

        if (!$model) {
            $model = new ClpCollegeImagePanel();
        }

        $request = Yii::$app->request->post();

        $model->college_id = $college_id;
        $oldImage = $model->logo_image;
        $oldBannerImage = $model->banner_image;

        if ($model->load($request)) {
            if (isset($model->logo_image)) {
                $logoImage = \yii\web\UploadedFile::getInstance($model, 'logo_image');
                if (isset($logoImage)) {
                    $logoImageName = md5(uniqid(rand(), true)) . '.' . $logoImage->getExtension();
                    $model->logo_image = $logoImageName;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($logoImageName, 'clp_college_logo'), $logoImage->tempName);
                    if ($saveImage) {
                        $model->logo_image = $logoImageName;
                    }
                }
            }
            if (isset($model->banner_image)) {
                $bannerImage = \yii\web\UploadedFile::getInstance($model, 'banner_image');
                if (isset($bannerImage)) {
                    $bannerImageName = md5(uniqid(rand(), true)) . '.' . $bannerImage->getExtension();
                    $model->banner_image = $bannerImageName;
                    $saveImage =  (new S3Service())->uploadFile(DataHelper::s3Path($bannerImageName, 'clp_college_banner'), $bannerImage->tempName);
                    if ($saveImage) {
                        $model->banner_image = $bannerImageName;
                    }
                }
            }

            if (empty($model->logo_image)) {
                $model->logo_image = $oldImage;
            }

            if (empty($model->banner_image)) {
                $model->banner_image = $oldBannerImage;
            }

            if ($model->save()) {
                return $this->redirect([
                    'view',
                    'id' => $model->id,
                    'clp_id' => $clp_id
                ]);
            }

            Yii::$app->session->setFlash('error', 'College has already been taken.');
        } else {
            return $this->render('create', [
                'model' => $model,
                'college_id' => $college_id,
                'clp_id' => $clp_id
            ]);
        }
    }

    /**
     * Updates an existing ClpCollegeImagePanel model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing ClpCollegeImagePanel model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ClpCollegeImagePanel model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ClpCollegeImagePanel the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ClpCollegeImagePanel::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }
}
