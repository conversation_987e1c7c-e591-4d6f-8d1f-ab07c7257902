<?php

namespace backend\controllers;

use Yii;
use common\models\ClpCollegeUsp;
use backend\models\ClpCollegeUspSearch;
use common\models\College;
use yii\base\Model;
use yii\web\Controller;
use yii\web\NotFoundHttpException;
use yii\filters\VerbFilter;

/**
 * ClpCollegeUspController implements the CRUD actions for ClpCollegeUsp model.
 */
class ClpCollegeUspController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'verbs' => [
                'class' => VerbFilter::className(),
                'actions' => [
                    'delete' => ['POST'],
                ],
            ],
        ];
    }

    /**
     * Lists all ClpCollegeUsp models.
     * @return mixed
     */
    public function actionIndex()
    {
        $searchModel = new ClpCollegeUspSearch();
        $dataProvider = $searchModel->search(Yii::$app->request->queryParams);

        return $this->render('index', [
            'searchModel' => $searchModel,
            'dataProvider' => $dataProvider,
        ]);
    }

    /**
     * Displays a single ClpCollegeUsp model.
     * @param integer $id
     * @return mixed
     */
    public function actionView($id)
    {
        return $this->render('view', [
            'model' => $this->findModel($id),
        ]);
    }

    /**
     * Creates a new ClpCollegeUsp model.
     * If creation is successful, the browser will be redirected to the 'view' page.
     * @return mixed
     */
    public function actionCreate()
    {
        $model = new ClpCollegeUsp();

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('create', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Updates an existing ClpCollegeUsp model.
     * If update is successful, the browser will be redirected to the 'view' page.
     * @param integer $id
     * @return mixed
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);

        if ($model->load(Yii::$app->request->post()) && $model->save()) {
            return $this->redirect(['view', 'id' => $model->id]);
        } else {
            return $this->render('update', [
                'model' => $model,
            ]);
        }
    }

    /**
     * Deletes an existing ClpCollegeUsp model.
     * If deletion is successful, the browser will be redirected to the 'index' page.
     * @param integer $id
     * @return mixed
     */
    public function actionDelete($id)
    {
        $this->findModel($id)->delete();

        return $this->redirect(['index']);
    }

    /**
     * Finds the ClpCollegeUsp model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param integer $id
     * @return ClpCollegeUsp the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = ClpCollegeUsp::findOne($id)) !== null) {
            return $model;
        } else {
            throw new NotFoundHttpException('The requested page does not exist.');
        }
    }

    public function actionTabularForm($college_id = '', $clp_id = '')
    {
        if (empty($college_id)) {
            $model = new ClpCollegeUsp();
            $models[] = $model;
        }

        $models = $this->findCollegeUspModel($college_id);
        $college = College::findOne(['id' => $college_id]);
        $collegeName = $college->name ?? '';

        if (empty($models)) {
            $model = new ClpCollegeUsp();
            $model->college_id = $college_id;
            $models[] = $model;
        }

        $request = Yii::$app->getRequest();

        if ($request->isPost) {
            $postData = Yii::$app->request->post('ClpCollegeUsp', []);
            $models = [];
            foreach ($postData as $i => $data) {
                $model = isset($data['id']) && !empty($data['id']) ? ClpCollegeUsp::findOne($data['id']) : new ClpCollegeUsp();
                $model->college_id = $college_id;
                $models[] = $model;
            }

            if (Model::loadMultiple($models, Yii::$app->request->post()) && Model::validateMultiple($models)) {
                foreach ($models as $index => $model) {
                    if (!$model->save()) {
                        Yii::$app->session->setFlash('error', 'Error saving: ' . json_encode($model->getErrors()));
                        return $this->render('_tabular_form', [
                            'models' => $models ?? [],
                            'collegeId' => $college_id,
                            'collegeName' => $collegeName,
                            'clp_id' => $clp_id
                        ]);
                    }
                }

                Yii::$app->session->setFlash('success', 'Data added successfully');
                $redirectCollegeId = isset($models[0]) ? $models[0]->college_id : '';

                return $this->redirect(['tabular-form', 'college_id' => $redirectCollegeId]);
            }
            Yii::$app->session->setFlash('error', 'Error saving: ' . json_encode($model->getErrors()));
            return $this->render('_tabular_form', [
                'models' => $models ?? [],
                'collegeId' => $college_id,
                'collegeName' => $collegeName,
                'clp_id' => $clp_id
            ]);
        }

        return $this->render('_tabular_form', [
            'models' => $models ?? [],
            'collegeId' => $college_id,
            'collegeName' => $collegeName,
            'clp_id' => $clp_id
        ]);
    }

    protected function findCollegeUspModel($college_id)
    {
        return ClpCollegeUsp::find()
            ->where(['college_id' => $college_id])
            ->all();
    }
}
